'use client';

import { motion } from 'motion/react';

interface Location {
  id: string;
  name: string;
  slug: string;
  description: string;
  image?: string;
  priceRanges?: any;
}

interface LocationsPageClientProps {
  locations: Location[];
}

export default function LocationsPageClient({ locations }: LocationsPageClientProps) {
  return (
    <section className="py-20 bg-gradient-to-br from-white via-emerald-50 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Popular <span className="bg-gradient-to-r from-emerald-600 to-emerald-500 bg-clip-text text-transparent">Locations</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Discover the most sought-after areas in Bali for long-term rentals and property investment.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {locations.map((location, index) => (
            <motion.div
              key={location.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl hover:shadow-2xl overflow-hidden transition-all duration-500 border border-white/50 hover:border-emerald-200/50 transform hover:-translate-y-2"
            >
              {/* Location Image */}
              <div className="relative h-64 overflow-hidden">
                <div 
                  className="absolute inset-0 bg-cover bg-center bg-no-repeat group-hover:scale-110 transition-transform duration-700"
                  style={{ 
                    backgroundImage: location.image 
                      ? `url(${location.image})` 
                      : `url(https://images.unsplash.com/photo-1537953773345-d172ccf13cf1?q=80&w=2070&auto=format&fit=crop)`
                  }}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent group-hover:from-black/70 transition-all duration-500" />
                
                {/* Location Badge */}
                <div className="absolute top-4 left-4">
                  <span className="bg-emerald-500/90 text-white px-4 py-2 rounded-full text-sm font-semibold backdrop-blur-sm">
                    Premium Location
                  </span>
                </div>

                {/* Location Title Overlay */}
                <div className="absolute bottom-4 left-4 right-4">
                  <h3 className="text-2xl font-bold text-white mb-2 group-hover:text-emerald-300 transition-colors duration-300">
                    {location.name}
                  </h3>
                </div>
              </div>

              {/* Location Content */}
              <div className="p-8">
                <p className="text-gray-600 mb-6 leading-relaxed line-clamp-3">
                  {location.description}
                </p>

                {/* Price Range Preview */}
                {location.priceRanges?.villa && (
                  <div className="mb-6 p-4 bg-emerald-50 rounded-2xl border border-emerald-100">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-emerald-700">Villa Rentals</span>
                      <span className="text-lg font-bold text-emerald-800">
                        ${location.priceRanges.villa.monthly?.min || 'N/A'} - ${location.priceRanges.villa.monthly?.max || 'N/A'}/mo
                      </span>
                    </div>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-4">
                  <a
                    href={`/locations/${location.slug}`}
                    className="flex-1 text-center bg-gradient-to-r from-emerald-500 to-emerald-600 text-white px-6 py-3 rounded-2xl hover:from-emerald-600 hover:to-emerald-700 transition-all duration-300 font-semibold shadow-lg hover:shadow-emerald-500/25 transform hover:scale-105"
                  >
                    <span className="flex items-center justify-center">
                      Explore Area
                      <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                      </svg>
                    </span>
                  </a>
                  <a
                    href={`/chatbot?query=rent-villa-in-${location.slug}`}
                    className="text-center bg-white/50 text-emerald-700 px-6 py-3 rounded-2xl hover:bg-white/70 transition-all duration-300 font-semibold border border-emerald-200 hover:border-emerald-300"
                  >
                    Find Properties
                  </a>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
