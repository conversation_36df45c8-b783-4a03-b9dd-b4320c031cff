import { CollectionConfig } from 'payload'
import { lexicalEditor } from '@payloadcms/richtext-lexical'

export const Locations: CollectionConfig = {
  slug: 'locations',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'slug', 'priority', 'updatedAt'],
  },
  access: {
    read: () => true, // Allow public read access for API testing
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      label: 'Location Name',
    },
    {
      name: 'slug',
      type: 'text',
      required: true,
      unique: true,
      label: 'URL Slug',
      admin: {
        description: 'Used in URLs (e.g., /canggu/)',
      },
    },
    {
      name: 'priority',
      type: 'select',
      required: true,
      options: [
        { label: 'Zeer Hoog', value: 'zeer-hoog' },
        { label: 'Hoog', value: 'hoog' },
        { label: 'Middel', value: 'middel' },
        { label: 'Laag', value: 'laag' },
      ],
      defaultValue: 'middel',
      admin: {
        description: 'SEO priority level for content creation',
      },
    },
    {
      name: 'description',
      type: 'text',
      required: true,
      label: 'Location Overview',
      admin: {
        description: 'Main description for the location',
      },
    },
    {
      name: 'lifestyle',
      type: 'textarea',
      label: 'Lifestyle & Amenities',
      admin: {
        description: 'Lifestyle information, dining, coworking, activities',
      },
    },
    {
      name: 'expatCommunity',
      type: 'richText',
      editor: lexicalEditor({}),
      label: 'Expat Community Info',
      admin: {
        description: 'Community size, demographics, popular hangouts',
      },
    },
    {
      name: 'transportation',
      type: 'richText',
      editor: lexicalEditor({}),
      label: 'Transportation & Accessibility',
      admin: {
        description: 'Airport distance, local transport, walkability',
      },
    },
    {
      name: 'practicalInfo',
      type: 'richText',
      editor: lexicalEditor({}),
      label: 'Practical Information',
      admin: {
        description: 'Banking, utilities, regulations, emergency contacts',
      },
    },
    // STORY-011: Comprehensive Content Fields
    {
      name: 'marketAnalysis',
      type: 'group',
      label: 'Market Analysis & Investment Data',
      fields: [
        {
          name: 'rentalYield',
          type: 'group',
          label: 'Rental Yield Information',
          fields: [
            {
              name: 'averageYield',
              type: 'number',
              label: 'Average Rental Yield (%)',
              admin: {
                description: 'Annual rental yield percentage',
              },
            },
            {
              name: 'yieldRange',
              type: 'text',
              label: 'Yield Range',
              admin: {
                description: 'e.g., "8-15% annually"',
              },
            },
            {
              name: 'seasonalDemand',
              type: 'richText',
              editor: lexicalEditor({}),
              label: 'Seasonal Demand Patterns',
              admin: {
                description: 'High/low seasons, occupancy rates, pricing variations',
              },
            },
          ],
        },
        {
          name: 'pricetrends',
          type: 'group',
          label: 'Property Price Trends',
          fields: [
            {
              name: 'priceGrowth',
              type: 'text',
              label: 'Annual Price Growth',
              admin: {
                description: 'e.g., "12-18% annually"',
              },
            },
            {
              name: 'marketTrends',
              type: 'richText',
              editor: lexicalEditor({}),
              label: 'Market Trends & Forecasts',
              admin: {
                description: 'Market analysis, future outlook, investment opportunities',
              },
            },
            {
              name: 'investmentHighlights',
              type: 'array',
              label: 'Investment Highlights',
              fields: [
                {
                  name: 'highlight',
                  type: 'text',
                  required: true,
                },
              ],
            },
          ],
        },
        {
          name: 'roiCalculation',
          type: 'group',
          label: 'ROI & Investment Calculations',
          fields: [
            {
              name: 'averageROI',
              type: 'text',
              label: 'Average ROI Timeline',
              admin: {
                description: 'e.g., "7-10 years payback period"',
              },
            },
            {
              name: 'investmentRisks',
              type: 'richText',
              editor: lexicalEditor({}),
              label: 'Investment Risks & Considerations',
            },
            {
              name: 'marketComparison',
              type: 'richText',
              editor: lexicalEditor({}),
              label: 'Comparison with Other Locations',
            },
          ],
        },
      ],
    },
    {
      name: 'priceRange',
      type: 'group',
      label: 'Price Ranges',
      fields: [
        {
          name: 'rentalMin',
          type: 'number',
          label: 'Rental Min (USD/month)',
        },
        {
          name: 'rentalMax',
          type: 'number',
          label: 'Rental Max (USD/month)',
        },
        {
          name: 'saleMin',
          type: 'number',
          label: 'Sale Min (USD)',
        },
        {
          name: 'saleMax',
          type: 'number',
          label: 'Sale Max (USD)',
        },
      ],
    },
    {
      name: 'amenities',
      type: 'array',
      label: 'Key Amenities',
      fields: [
        {
          name: 'amenity',
          type: 'text',
          required: true,
        },
      ],
    },
    {
      name: 'neighborhoods',
      type: 'array',
      label: 'Neighborhoods/Sub-areas',
      fields: [
        {
          name: 'name',
          type: 'text',
          required: true,
        },
        {
          name: 'description',
          type: 'textarea',
          required: true,
        },
        {
          name: 'bestFor',
          type: 'text',
          label: 'Best For',
          admin: {
            description: 'e.g., "Digital nomads", "Families", "Surfers"',
          },
        },
      ],
    },
    // STORY-011: Legal & Regulatory Information
    {
      name: 'legalInfo',
      type: 'group',
      label: 'Legal & Regulatory Information',
      fields: [
        {
          name: 'foreignOwnership',
          type: 'richText',
          editor: lexicalEditor({}),
          label: 'Foreign Ownership Rules',
          admin: {
            description: 'Freehold vs leasehold options, legal structures, restrictions',
          },
        },
        {
          name: 'visaRequirements',
          type: 'richText',
          editor: lexicalEditor({}),
          label: 'Visa Requirements',
          admin: {
            description: 'Visa types, requirements, processes for property owners',
          },
        },
        {
          name: 'taxImplications',
          type: 'richText',
          editor: lexicalEditor({}),
          label: 'Tax Implications',
          admin: {
            description: 'Property taxes, income taxes, capital gains, tax planning',
          },
        },
        {
          name: 'localRegulations',
          type: 'richText',
          editor: lexicalEditor({}),
          label: 'Local Regulations',
          admin: {
            description: 'Building codes, zoning laws, rental restrictions',
          },
        },
      ],
    },
    // STORY-011: Cost of Living & Infrastructure
    {
      name: 'costOfLiving',
      type: 'group',
      label: 'Cost of Living Breakdown',
      fields: [
        {
          name: 'monthlyBudget',
          type: 'group',
          label: 'Monthly Budget Estimates',
          fields: [
            {
              name: 'budget',
              type: 'text',
              label: 'Budget Range',
              admin: {
                description: 'e.g., "$1,500-3,000/month"',
              },
            },
            {
              name: 'breakdown',
              type: 'richText',
              editor: lexicalEditor({}),
              label: 'Detailed Breakdown',
              admin: {
                description: 'Housing, food, transport, utilities, entertainment costs',
              },
            },
          ],
        },
        {
          name: 'utilities',
          type: 'richText',
          editor: lexicalEditor({}),
          label: 'Utilities & Services',
          admin: {
            description: 'Internet, electricity, water, waste, security costs',
          },
        },
        {
          name: 'lifestyle',
          type: 'richText',
          editor: lexicalEditor({}),
          label: 'Lifestyle Costs',
          admin: {
            description: 'Dining, entertainment, fitness, transport, shopping',
          },
        },
      ],
    },
    // STORY-011: Infrastructure & Services
    {
      name: 'infrastructure',
      type: 'group',
      label: 'Infrastructure & Services',
      fields: [
        {
          name: 'internet',
          type: 'richText',
          editor: lexicalEditor({}),
          label: 'Internet & Connectivity',
          admin: {
            description: 'Speed, reliability, providers, coworking spaces',
          },
        },
        {
          name: 'healthcare',
          type: 'richText',
          editor: lexicalEditor({}),
          label: 'Healthcare Services',
          admin: {
            description: 'Hospitals, clinics, pharmacies, insurance, emergency services',
          },
        },
        {
          name: 'education',
          type: 'richText',
          editor: lexicalEditor({}),
          label: 'Education & Schools',
          admin: {
            description: 'International schools, language schools, universities',
          },
        },
        {
          name: 'banking',
          type: 'richText',
          editor: lexicalEditor({}),
          label: 'Banking & Finance',
          admin: {
            description: 'Banks, ATMs, money exchange, financial services',
          },
        },
        {
          name: 'safety',
          type: 'richText',
          editor: lexicalEditor({}),
          label: 'Safety & Security',
          admin: {
            description: 'Crime rates, safety tips, emergency contacts, security services',
          },
        },
      ],
    },
    {
      name: 'coordinates',
      type: 'point',
      label: 'GPS Coordinates',
      admin: {
        description: 'Used for maps integration',
      },
    },
    {
      name: 'featuredImage',
      type: 'upload',
      relationTo: 'media',
      label: 'Featured Image',
      admin: {
        description: 'Main hero image for the location',
      },
    },
    {
      name: 'gallery',
      type: 'array',
      label: 'Image Gallery',
      fields: [
        {
          name: 'image',
          type: 'upload',
          relationTo: 'media',
          required: true,
        },
        {
          name: 'caption',
          type: 'text',
        },
      ],
    },
    // STORY-011: SEO & Content Optimization
    {
      name: 'seoContent',
      type: 'group',
      label: 'SEO & Content Optimization',
      fields: [
        {
          name: 'targetKeywords',
          type: 'array',
          label: 'Target Keywords',
          fields: [
            {
              name: 'keyword',
              type: 'text',
              required: true,
            },
          ],
          admin: {
            description: 'Primary keywords to target for this location',
          },
        },
        {
          name: 'metaDescription',
          type: 'textarea',
          label: 'Custom Meta Description',
          maxLength: 160,
          admin: {
            description: 'Custom meta description for this location (max 160 chars)',
          },
        },
        {
          name: 'contentSummary',
          type: 'richText',
          editor: lexicalEditor({}),
          label: 'Content Summary',
          admin: {
            description: 'Brief summary for featured snippets and social sharing',
          },
        },
        {
          name: 'relatedLocations',
          type: 'relationship',
          relationTo: 'locations',
          hasMany: true,
          label: 'Related Locations',
          admin: {
            description: 'Locations to link to for internal SEO',
          },
        },
      ],
    },
    {
      name: 'faq',
      type: 'array',
      label: 'Frequently Asked Questions',
      fields: [
        {
          name: 'question',
          type: 'text',
          required: true,
        },
        {
          name: 'answer',
          type: 'richText',
          editor: lexicalEditor({}),
          required: true,
        },
      ],
    },
    // SEO fields will be added by the SEO plugin
  ],
  hooks: {
    beforeChange: [
      ({ data }) => {
        // Auto-generate slug from name if not provided
        if (data.name && !data.slug) {
          data.slug = data.name
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/(^-|-$)/g, '')
        }
        return data
      },
    ],
  },
}
