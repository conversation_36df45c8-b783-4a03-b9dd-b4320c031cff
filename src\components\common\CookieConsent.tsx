/**
 * <PERSON><PERSON> Consent Banner Component
 * PRD Requirement: <PERSON><PERSON> consent banner for GDPR compliance
 */

'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

const COOKIE_CONSENT_KEY = 'bali-real-estate-cookie-consent';

export function CookieConsent() {
  const [showBanner, setShowBanner] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    // Check if user has already given consent
    const hasConsent = localStorage.getItem(COOKIE_CONSENT_KEY);
    if (!hasConsent) {
      setShowBanner(true);
    }
    setIsLoaded(true);
  }, []);

  const handleAccept = () => {
    localStorage.setItem(COOKIE_CONSENT_KEY, 'accepted');
    setShowBanner(false);
    
    // Enable Google Analytics if not already enabled
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('consent', 'update', {
        analytics_storage: 'granted'
      });
    }
  };

  const handleDecline = () => {
    localStorage.setItem(COOKIE_CONSENT_KEY, 'declined');
    setShowBanner(false);
    
    // Disable Google Analytics
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('consent', 'update', {
        analytics_storage: 'denied'
      });
    }
  };

  // Don't render until loaded to prevent hydration mismatch
  if (!isLoaded || !showBanner) {
    return null;
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t-4 border-emerald-500 shadow-2xl">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          {/* Content */}
          <div className="flex-1">
            <div className="flex items-start gap-3">
              {/* Cookie Icon */}
              <div className="flex-shrink-0 mt-1">
                <svg 
                  className="w-5 h-5 text-emerald-600" 
                  fill="currentColor" 
                  viewBox="0 0 20 20"
                >
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              
              {/* Text */}
              <div>
                <p className="text-sm text-gray-700 leading-relaxed">
                  We use cookies to enhance your browsing experience, analyze site traffic, and provide personalized content. 
                  This helps us improve our services and show you relevant property recommendations.{' '}
                  <Link 
                    href="/privacy-policy" 
                    className="text-emerald-600 hover:text-emerald-700 underline underline-offset-2"
                  >
                    Learn more about our privacy policy
                  </Link>
                  .
                </p>
              </div>
            </div>
          </div>

          {/* Buttons */}
          <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
            <button
              onClick={handleDecline}
              className="px-6 py-3 text-sm font-semibold text-gray-700 hover:text-gray-900 bg-white hover:bg-gray-50 border-2 border-gray-300 hover:border-gray-400 rounded-lg transition-all duration-200 w-full sm:w-auto shadow-sm"
            >
              Decline
            </button>
            <button
              onClick={handleAccept}
              className="px-6 py-3 text-sm font-semibold text-white bg-emerald-600 hover:bg-emerald-700 border-2 border-emerald-600 hover:border-emerald-700 rounded-lg transition-all duration-200 w-full sm:w-auto shadow-lg"
            >
              Accept Cookies
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default CookieConsent;
