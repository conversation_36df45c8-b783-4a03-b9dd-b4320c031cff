/**
 * Design System - Breakpoints
 * Bali Real Estate Website
 * 
 * This file defines the responsive breakpoint system for
 * consistent responsive design across all components.
 */

// Breakpoint values (mobile-first approach)
export const breakpoints = {
  xs: '0px',        // Extra small devices (phones)
  sm: '640px',      // Small devices (large phones)
  md: '768px',      // Medium devices (tablets)
  lg: '1024px',     // Large devices (laptops)
  xl: '1280px',     // Extra large devices (desktops)
  '2xl': '1536px',  // 2X large devices (large desktops)
} as const;

// Breakpoint ranges for media queries
export const breakpointRanges = {
  xs: {
    min: 0,
    max: 639,
  },
  sm: {
    min: 640,
    max: 767,
  },
  md: {
    min: 768,
    max: 1023,
  },
  lg: {
    min: 1024,
    max: 1279,
  },
  xl: {
    min: 1280,
    max: 1535,
  },
  '2xl': {
    min: 1536,
    max: Infinity,
  },
} as const;

// Device categories
export const deviceCategories = {
  mobile: {
    breakpoints: ['xs', 'sm'],
    description: 'Mobile phones and small tablets',
    maxWidth: breakpointRanges.sm.max,
  },
  tablet: {
    breakpoints: ['md'],
    description: 'Tablets and small laptops',
    minWidth: breakpointRanges.md.min,
    maxWidth: breakpointRanges.md.max,
  },
  desktop: {
    breakpoints: ['lg', 'xl', '2xl'],
    description: 'Laptops and desktop computers',
    minWidth: breakpointRanges.lg.min,
  },
} as const;

// Container max widths for each breakpoint
export const containerMaxWidths = {
  xs: '100%',
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
} as const;

// Grid columns for each breakpoint
export const gridColumns = {
  xs: 4,   // 4 columns on mobile
  sm: 6,   // 6 columns on large mobile
  md: 8,   // 8 columns on tablet
  lg: 12,  // 12 columns on desktop
  xl: 12,  // 12 columns on large desktop
  '2xl': 12, // 12 columns on extra large desktop
} as const;

// Media query helpers
export const mediaQueries = {
  // Min-width media queries (mobile-first)
  up: {
    xs: `@media (min-width: ${breakpoints.xs})`,
    sm: `@media (min-width: ${breakpoints.sm})`,
    md: `@media (min-width: ${breakpoints.md})`,
    lg: `@media (min-width: ${breakpoints.lg})`,
    xl: `@media (min-width: ${breakpoints.xl})`,
    '2xl': `@media (min-width: ${breakpoints['2xl']})`,
  },

  // Max-width media queries (desktop-first)
  down: {
    xs: `@media (max-width: ${breakpointRanges.xs.max}px)`,
    sm: `@media (max-width: ${breakpointRanges.sm.max}px)`,
    md: `@media (max-width: ${breakpointRanges.md.max}px)`,
    lg: `@media (max-width: ${breakpointRanges.lg.max}px)`,
    xl: `@media (max-width: ${breakpointRanges.xl.max}px)`,
  },

  // Range media queries (between breakpoints)
  between: {
    'xs-sm': `@media (min-width: ${breakpoints.xs}) and (max-width: ${breakpointRanges.sm.max}px)`,
    'sm-md': `@media (min-width: ${breakpoints.sm}) and (max-width: ${breakpointRanges.md.max}px)`,
    'md-lg': `@media (min-width: ${breakpoints.md}) and (max-width: ${breakpointRanges.lg.max}px)`,
    'lg-xl': `@media (min-width: ${breakpoints.lg}) and (max-width: ${breakpointRanges.xl.max}px)`,
    'xl-2xl': `@media (min-width: ${breakpoints.xl}) and (max-width: ${breakpointRanges['2xl'].max}px)`,
  },

  // Device-specific media queries
  mobile: `@media (max-width: ${breakpointRanges.sm.max}px)`,
  tablet: `@media (min-width: ${breakpoints.md}) and (max-width: ${breakpointRanges.md.max}px)`,
  desktop: `@media (min-width: ${breakpoints.lg})`,

  // Orientation media queries
  landscape: '@media (orientation: landscape)',
  portrait: '@media (orientation: portrait)',

  // High DPI media queries
  retina: '@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi)',
  
  // Reduced motion media query
  reducedMotion: '@media (prefers-reduced-motion: reduce)',
  
  // Dark mode media query
  darkMode: '@media (prefers-color-scheme: dark)',
} as const;

// Responsive utilities
export const responsiveUtils = {
  /**
   * Check if current screen size matches breakpoint
   */
  isBreakpoint: (breakpoint: keyof typeof breakpoints) => {
    if (typeof window === 'undefined') return false;
    const width = window.innerWidth;
    const range = breakpointRanges[breakpoint];
    return width >= range.min && width <= range.max;
  },

  /**
   * Check if current screen size is mobile
   */
  isMobile: () => {
    if (typeof window === 'undefined') return false;
    return window.innerWidth <= breakpointRanges.sm.max;
  },

  /**
   * Check if current screen size is tablet
   */
  isTablet: () => {
    if (typeof window === 'undefined') return false;
    const width = window.innerWidth;
    return width >= breakpointRanges.md.min && width <= breakpointRanges.md.max;
  },

  /**
   * Check if current screen size is desktop
   */
  isDesktop: () => {
    if (typeof window === 'undefined') return false;
    return window.innerWidth >= breakpointRanges.lg.min;
  },

  /**
   * Get current breakpoint
   */
  getCurrentBreakpoint: (): keyof typeof breakpoints => {
    if (typeof window === 'undefined') return 'lg';
    
    const width = window.innerWidth;
    
    if (width >= parseInt(breakpoints['2xl'])) return '2xl';
    if (width >= parseInt(breakpoints.xl)) return 'xl';
    if (width >= parseInt(breakpoints.lg)) return 'lg';
    if (width >= parseInt(breakpoints.md)) return 'md';
    if (width >= parseInt(breakpoints.sm)) return 'sm';
    return 'xs';
  },

  /**
   * Get container max width for current breakpoint
   */
  getContainerMaxWidth: (breakpoint?: keyof typeof breakpoints) => {
    const bp = breakpoint || responsiveUtils.getCurrentBreakpoint();
    return containerMaxWidths[bp];
  },

  /**
   * Get grid columns for current breakpoint
   */
  getGridColumns: (breakpoint?: keyof typeof breakpoints) => {
    const bp = breakpoint || responsiveUtils.getCurrentBreakpoint();
    return gridColumns[bp];
  },
} as const;

// Responsive component props helper
export const responsiveProps = {
  /**
   * Create responsive prop object
   */
  create: <T>(values: Partial<Record<keyof typeof breakpoints, T>>) => {
    return values;
  },

  /**
   * Get value for current breakpoint with fallback
   */
  getValue: <T>(
    responsiveValue: Partial<Record<keyof typeof breakpoints, T>>,
    fallback: T,
    currentBreakpoint?: keyof typeof breakpoints
  ): T => {
    const bp = currentBreakpoint || responsiveUtils.getCurrentBreakpoint();
    
    // Try current breakpoint first
    if (responsiveValue[bp] !== undefined) {
      return responsiveValue[bp]!;
    }
    
    // Fallback to smaller breakpoints
    const breakpointOrder: (keyof typeof breakpoints)[] = ['2xl', 'xl', 'lg', 'md', 'sm', 'xs'];
    const currentIndex = breakpointOrder.indexOf(bp);
    
    for (let i = currentIndex + 1; i < breakpointOrder.length; i++) {
      const fallbackBp = breakpointOrder[i];
      if (responsiveValue[fallbackBp] !== undefined) {
        return responsiveValue[fallbackBp]!;
      }
    }
    
    return fallback;
  },
} as const;

// Export for Tailwind CSS configuration
export const tailwindBreakpoints = {
  screens: breakpoints,
} as const;

// TypeScript types
export type Breakpoint = keyof typeof breakpoints;
export type DeviceCategory = keyof typeof deviceCategories;
export type ResponsiveValue<T> = Partial<Record<Breakpoint, T>>;

// Breakpoint hooks (for React components)
export const breakpointHooks = {
  /**
   * Hook to get current breakpoint (would be implemented with useEffect/useState)
   */
  useBreakpoint: () => {
    // This would be implemented as a React hook
    // For now, return a static value
    return responsiveUtils.getCurrentBreakpoint();
  },

  /**
   * Hook to check if current breakpoint matches
   */
  useBreakpointMatch: (breakpoint: Breakpoint) => {
    // This would be implemented as a React hook
    return responsiveUtils.isBreakpoint(breakpoint);
  },

  /**
   * Hook to get responsive value
   */
  useResponsiveValue: <T>(responsiveValue: ResponsiveValue<T>, fallback: T) => {
    // This would be implemented as a React hook
    return responsiveProps.getValue(responsiveValue, fallback);
  },
} as const;
