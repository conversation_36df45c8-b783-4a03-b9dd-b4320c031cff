import { Metadata } from 'next'
import Link from 'next/link'
import { getLocations } from '@/lib/payload'
import { AdvancedSchema } from '@/components/seo/AdvancedSchema'
import { CleanModernHero as ModernHero } from '@/components/ui/CleanModernHero'
import { ChatBubble } from '@/components/ui/ChatBubble'

export const metadata: Metadata = {
  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'https://bali-real-estate.com'),
  title: {
    default: 'Bali Real Estate Guide | Best Areas to Live in Bali | Long Term Rental Bali',
    template: '%s | Bali Real Estate'
  },
  description: 'Complete bali real estate guide for expats & digital nomads. Discover best areas to live in bali, long term rental bali options, bali expat housing. Expert guidance for bali property for sale.',
  keywords: [
    'bali real estate guide',
    'best areas to live in bali',
    'long term rental bali',
    'bali expat housing',
    'bali property for sale',
    'bali digital nomad community',
    'moving to bali checklist',
    'bali real estate agent',
    'cost of living in bali',
    'bali investment hotspots',
    'invest in bali property',
    'bali property consultation'
  ],
  authors: [{ name: 'Bali Real Estate Experts' }],
  creator: 'Bali Real Estate',
  publisher: 'Bali Real Estate',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  openGraph: {
    title: 'Bali Real Estate - Long-term Rentals & Property Sales',
    description: 'Find your perfect long-term rental or property in Bali with expert local guidance. Specializing in premium locations like Canggu, Ubud, and Seminyak.',
    type: 'website',
    locale: 'en_US',
    url: '/',
    siteName: 'Bali Real Estate',
    images: [
      {
        url: '/images/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Bali Real Estate - Premium Villas and Properties',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Bali Real Estate - Long-term Rentals & Property Sales',
    description: 'Find your perfect long-term rental or property in Bali with expert local guidance.',
    images: ['/images/twitter-image.jpg'],
    creator: '@BaliRealEstate',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  alternates: {
    canonical: '/',
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
  },
}



export default async function HomePage() {
  // Temporary: Skip database for testing
  // const locations = await getLocations()
  const locations = [
    {
      id: '1',
      slug: 'canggu',
      name: 'Canggu',
      priority: 'zeer-hoog',
      description: 'Popular surf town with vibrant nightlife and coworking spaces',
      priceRange: { rentalMin: 8000000, rentalMax: 25000000 }
    },
    {
      id: '2',
      slug: 'ubud',
      name: 'Ubud',
      priority: 'hoog',
      description: 'Cultural heart of Bali with lush rice terraces and wellness retreats',
      priceRange: { rentalMin: 6000000, rentalMax: 20000000 }
    },
    {
      id: '3',
      slug: 'seminyak',
      name: 'Seminyak',
      priority: 'zeer-hoog',
      description: 'Upscale beach destination with luxury resorts and fine dining',
      priceRange: { rentalMin: 12000000, rentalMax: 35000000 }
    },
    {
      id: '4',
      slug: 'sanur',
      name: 'Sanur',
      priority: 'hoog',
      description: 'Traditional beachside town perfect for families and long-term stays',
      priceRange: { rentalMin: 7000000, rentalMax: 18000000 }
    },
    {
      id: '5',
      slug: 'jimbaran',
      name: 'Jimbaran',
      priority: 'hoog',
      description: 'Luxury bay area known for seafood restaurants and upscale villas',
      priceRange: { rentalMin: 10000000, rentalMax: 30000000 }
    },
    {
      id: '6',
      slug: 'uluwatu',
      name: 'Uluwatu',
      priority: 'gemiddeld',
      description: 'Clifftop paradise with world-class surf breaks and stunning views',
      priceRange: { rentalMin: 9000000, rentalMax: 28000000 }
    }
  ]

  // Create property combinations from CMS data
  const priorityCombinations = locations.map((location: any) => {
    // Handle price range formatting in IDR (realistic Bali prices)
    let priceRange = 'Contact for pricing'
    if (location.priceRange?.rentalMin && location.priceRange?.rentalMax) {
      // Convert to realistic IDR prices (original was in IDR, not USD)
      const minPrice = Math.round(location.priceRange.rentalMin / 1000000) // Convert to millions
      const maxPrice = Math.round(location.priceRange.rentalMax / 1000000)
      priceRange = `${minPrice}-${maxPrice} M IDR/month`
    }

    return {
      location: location.slug,
      locationName: location.name,
      propertyType: 'villa',
      transactionType: 'long-term-rental',
      description: typeof location.description === 'string' ? location.description : 'Discover this amazing location in Bali',
      priceRange
    }
  })

  // Structured Data for SEO
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "RealEstateAgent",
    "name": "Bali Real Estate",
    "description": "Expert real estate services in Bali specializing in long-term rentals and property sales for expats, digital nomads, and investors.",
    "url": process.env.NEXT_PUBLIC_SITE_URL || "https://bali-real-estate.com",
    "logo": `${process.env.NEXT_PUBLIC_SITE_URL || "https://bali-real-estate.com"}/images/logo.png`,
    "image": `${process.env.NEXT_PUBLIC_SITE_URL || "https://bali-real-estate.com"}/images/og-image.jpg`,
    "telephone": "+62-XXX-XXX-XXXX",
    "email": "<EMAIL>",
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "ID",
      "addressRegion": "Bali",
      "addressLocality": "Bali",
      "streetAddress": "Bali, Indonesia"
    },
    "areaServed": [
      {
        "@type": "City",
        "name": "Canggu",
        "addressRegion": "Bali",
        "addressCountry": "Indonesia"
      },
      {
        "@type": "City",
        "name": "Ubud",
        "addressRegion": "Bali",
        "addressCountry": "Indonesia"
      },
      {
        "@type": "City",
        "name": "Seminyak",
        "addressRegion": "Bali",
        "addressCountry": "Indonesia"
      }
    ],
    "serviceType": ["Real Estate Sales", "Property Rental", "Property Management"],
    "priceRange": "$$-$$$"
  }

  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "Bali Real Estate",
    "url": process.env.NEXT_PUBLIC_SITE_URL || "https://bali-real-estate.com",
    "description": "Find your perfect long-term rental or property in Bali with expert local guidance.",
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${process.env.NEXT_PUBLIC_SITE_URL || "https://bali-real-estate.com"}/search?q={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    }
  }

  return (
    <main className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100">
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationSchema),
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(websiteSchema),
        }}
      />

      {/* Premium Hero Section */}
      <ModernHero
        title="Find Your Perfect Home in Bali"
        subtitle="Expert guidance for long-term rentals and property investments. Discover premium locations like Canggu, Seminyak, and Ubud with our specialized local knowledge."
        backgroundImage="https://images.unsplash.com/photo-1537953773345-d172ccf13cf1?q=80&w=2070&auto=format&fit=crop"
        primaryCTA={{
          text: "Start Your Search",
          href: "https://app.balipropertyscout.com?query=general-consultation"
        }}
        secondaryCTA={{
          text: "Browse Locations",
          href: "/locations"
        }}
      />

      {/* Premium Property Showcase */}
      <section className="py-24 relative">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-br from-emerald-50/50 via-transparent to-teal-50/50" />
        <div className="absolute inset-0 opacity-40" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }} />

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
              <span className="bg-gradient-to-r from-emerald-600 to-emerald-700 bg-clip-text text-transparent">
                Premium Properties
              </span>
              <br />
              <span className="text-gray-700">in Bali's Best Locations</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Discover exceptional villas and properties in Bali's most sought-after neighborhoods.
              Each location offers unique advantages for your lifestyle and investment goals.
            </p>
          </div>

          {/* Premium Property Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {priorityCombinations.map((combo: any, index: number) => (
              <Link key={index} href={`/${combo.location}/${combo.propertyType}/${combo.transactionType}`} className="block group">
                <div className="relative bg-white rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-500 overflow-hidden border border-gray-100 hover:border-emerald-200 transform hover:-translate-y-2 hover:scale-[1.02]">


                  {/* Price Badge */}
                  <div className="absolute top-6 right-6 z-20">
                    <div className="px-4 py-2 rounded-full text-sm font-bold bg-black/80 text-white backdrop-blur-sm">
                      {combo.priceRange}
                    </div>
                  </div>

                  {/* Image Section */}
                  <div className="relative h-48 overflow-hidden rounded-t-3xl">
                    <div
                      className="w-full h-full bg-cover bg-center transition-transform duration-500 group-hover:scale-110"
                      style={{
                        backgroundImage: `url(${
                          combo.location === 'canggu' ? "https://images.unsplash.com/photo-1571896349842-33c89424de2d?q=80&w=2080&auto=format&fit=crop" :
                          combo.location === 'ubud' ? "https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?q=80&w=2070&auto=format&fit=crop" :
                          combo.location === 'seminyak' ? "https://images.unsplash.com/photo-1540541338287-41700207dee6?q=80&w=2070&auto=format&fit=crop" :
                          combo.location === 'sanur' ? "https://images.unsplash.com/photo-1559827260-dc66d52bef19?q=80&w=2070&auto=format&fit=crop" :
                          combo.location === 'jimbaran' ? "https://images.unsplash.com/photo-1566073771259-6a8506099945?q=80&w=2070&auto=format&fit=crop" :
                          "https://images.unsplash.com/photo-1537953773345-d172ccf13cf1?q=80&w=2070&auto=format&fit=crop"
                        })`
                      }}
                    />

                    {/* Gradient Overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />

                    {/* Location Badge */}
                    <div className="absolute bottom-4 left-6">
                      <div className="flex items-center space-x-2 text-white">
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                        </svg>
                        <span className="text-sm font-medium">{combo.locationName}</span>
                      </div>
                    </div>
                  </div>

                  {/* Content Section */}
                  <div className="p-6">
                    {/* Title */}
                    <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-emerald-600 transition-colors duration-300">
                      {combo.locationName} {combo.propertyType.charAt(0).toUpperCase() + combo.propertyType.slice(1)}
                    </h3>

                    {/* Description */}
                    <p className="text-gray-600 mb-4 leading-relaxed text-sm">
                      {combo.description}
                    </p>

                    {/* Location-Specific Info */}
                    <div className="mb-4">
                      <div className="text-sm text-gray-600 leading-relaxed">
                        {combo.location === 'canggu' && (
                          <div className="space-y-1">
                            <p>• Popular surf destination with vibrant nightlife</p>
                            <p>• Strong coworking community for digital nomads</p>
                            <p>• Beach clubs and trendy restaurants nearby</p>
                          </div>
                        )}
                        {combo.location === 'ubud' && (
                          <div className="space-y-1">
                            <p>• Cultural heart with rice terraces and temples</p>
                            <p>• Wellness retreats and yoga studios</p>
                            <p>• Art galleries and traditional markets</p>
                          </div>
                        )}
                        {combo.location === 'seminyak' && (
                          <div className="space-y-1">
                            <p>• Upscale beach destination with luxury amenities</p>
                            <p>• Fine dining restaurants and boutique shopping</p>
                            <p>• Premium beach clubs and spas</p>
                          </div>
                        )}
                        {combo.location === 'sanur' && (
                          <div className="space-y-1">
                            <p>• Family-friendly with calm beaches</p>
                            <p>• Traditional Balinese atmosphere</p>
                            <p>• International schools and healthcare nearby</p>
                          </div>
                        )}
                        {combo.location === 'jimbaran' && (
                          <div className="space-y-1">
                            <p>• Famous for seafood restaurants on the beach</p>
                            <p>• Luxury resorts and upscale villas</p>
                            <p>• Close to airport and golf courses</p>
                          </div>
                        )}
                        {combo.location === 'uluwatu' && (
                          <div className="space-y-1">
                            <p>• Clifftop location with stunning ocean views</p>
                            <p>• World-class surf breaks and beach clubs</p>
                            <p>• Dramatic sunsets and temple visits</p>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* CTA */}
                    <div className="flex items-center justify-between">
                      <span className="font-semibold text-emerald-600 group-hover:underline transition-all duration-300">
                        View Details
                      </span>

                      <div className="w-12 h-12 bg-emerald-100 rounded-full flex items-center justify-center transition-all duration-300 group-hover:bg-emerald-200 group-hover:translate-x-1">
                        <svg className="w-5 h-5 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </div>
                    </div>
                  </div>

                  {/* Hover Glow Effect */}
                  <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-transparent via-emerald-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none" />
                </div>
              </Link>
            ))}
          </div>

          {/* View All Properties CTA */}
          <div className="text-center mt-16">
            <Link
              href="/locations"
              className="inline-flex items-center bg-gradient-to-r from-emerald-600 to-emerald-700 text-white px-8 py-4 rounded-2xl font-semibold text-lg shadow-xl hover:shadow-2xl hover:shadow-emerald-500/25 transition-all duration-300 transform hover:scale-105"
            >
              Explore All Properties
              <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </Link>
          </div>
        </div>
      </section>

      {/* Premium Contact Section */}
      <section className="py-24 relative overflow-hidden">
        {/* Light Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-gray-50 via-white to-emerald-50" />
        <div className="absolute inset-0 opacity-30" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`
        }} />

        {/* Floating Elements */}
        <div className="absolute top-20 left-10 w-2 h-2 bg-emerald-400/30 rounded-full animate-float" />
        <div className="absolute top-40 right-20 w-1 h-1 bg-teal-400/40 rounded-full animate-float" style={{ animationDelay: '1s' }} />
        <div className="absolute bottom-32 left-1/4 w-1.5 h-1.5 bg-blue-400/35 rounded-full animate-float" style={{ animationDelay: '2s' }} />

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Left Content */}
            <div className="text-gray-900">
              <h2 className="text-4xl sm:text-5xl font-bold mb-8">
                <span className="text-gray-900">
                  Ready to Find Your
                </span>
                <br />
                <span className="bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                  Perfect Bali Property?
                </span>
              </h2>

              <p className="text-xl text-gray-600 mb-12 leading-relaxed">
                Our local experts are here to help you navigate the Bali real estate market.
                Whether you're looking for a long-term rental, investment property, or your dream home,
                we'll guide you through every step of the process.
              </p>

              {/* Premium Features */}
              <div className="space-y-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0 w-12 h-12 bg-emerald-100 rounded-full flex items-center justify-center border border-emerald-200">
                    <svg className="w-6 h-6 text-emerald-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-semibold text-gray-900">Free Consultation & Market Analysis</h3>
                    <p className="text-gray-600">Comprehensive property evaluation and market insights</p>
                  </div>
                </div>

                <div className="flex items-center">
                  <div className="flex-shrink-0 w-12 h-12 bg-teal-100 rounded-full flex items-center justify-center border border-teal-200">
                    <svg className="w-6 h-6 text-teal-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-semibold text-gray-900">Local Expertise & Insider Knowledge</h3>
                    <p className="text-gray-600">Deep understanding of Bali's unique property market</p>
                  </div>
                </div>

                <div className="flex items-center">
                  <div className="flex-shrink-0 w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center border border-blue-200">
                    <svg className="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-semibold text-gray-900">End-to-End Support</h3>
                    <p className="text-gray-600">Complete assistance from search to settlement</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Content - Premium Form */}
            <div className="bg-white rounded-3xl shadow-2xl p-8 border border-gray-200">
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold text-gray-900 mb-3">Start Your Bali Journey</h2>
                <p className="text-gray-600 text-lg">Get personalized property recommendations</p>
              </div>

              <div className="space-y-6">
                <div>
                  <input
                    type="text"
                    placeholder="Your Name *"
                    className="w-full px-4 py-4 border-2 border-gray-200 rounded-xl transition-all duration-300 focus:outline-none focus:ring-0 focus:border-emerald-500 text-gray-900 placeholder-gray-500"
                  />
                </div>

                <div>
                  <input
                    type="email"
                    placeholder="Email Address *"
                    className="w-full px-4 py-4 border-2 border-gray-200 rounded-xl transition-all duration-300 focus:outline-none focus:ring-0 focus:border-emerald-500 text-gray-900 placeholder-gray-500"
                  />
                </div>

                <div>
                  <input
                    type="tel"
                    placeholder="Phone Number (optional)"
                    className="w-full px-4 py-4 border-2 border-gray-200 rounded-xl transition-all duration-300 focus:outline-none focus:ring-0 focus:border-emerald-500 text-gray-900 placeholder-gray-500"
                  />
                </div>

                <div>
                  <select className="w-full px-4 py-4 border-2 border-gray-200 rounded-xl transition-all duration-300 focus:outline-none focus:ring-0 focus:border-emerald-500 text-gray-900">
                    <option value="">When are you looking? *</option>
                    <option value="immediately">Immediately</option>
                    <option value="1-3-months">1-3 months</option>
                    <option value="3-6-months">3-6 months</option>
                    <option value="6-12-months">6-12 months</option>
                    <option value="just-browsing">Just browsing</option>
                  </select>
                </div>

                <div>
                  <textarea
                    rows={4}
                    placeholder="Tell us about your requirements..."
                    className="w-full px-4 py-4 border-2 border-gray-200 rounded-xl transition-all duration-300 focus:outline-none focus:ring-0 focus:border-emerald-500 resize-none text-gray-900 placeholder-gray-500"
                  />
                </div>

                <div>
                  <label className="flex items-start space-x-3 cursor-pointer">
                    <input
                      type="checkbox"
                      className="mt-1 w-5 h-5 text-emerald-600 border-2 border-gray-300 rounded focus:ring-emerald-500 focus:ring-2"
                    />
                    <span className="text-sm text-gray-600">
                      I consent to processing of my data for this inquiry. *
                    </span>
                  </label>
                </div>

                <button
                  type="submit"
                  className="w-full bg-gradient-to-r from-emerald-600 to-emerald-700 text-white font-semibold py-4 px-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02]"
                >
                  Start Your Search
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Premium Location Coverage */}
      <section className="py-24 relative">
        {/* Premium Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-white via-gray-50 to-emerald-50/30" />
        <div className="absolute inset-0 opacity-50" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23000000' fill-opacity='0.03' fill-rule='evenodd'%3E%3Cpath d='m0 40l40-40h-40v40zm40 0v-40h-40l40 40z'/%3E%3C/g%3E%3C/svg%3E")`
        }} />

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Section Header */}
          <div className="text-center mb-20">
            <h2 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
              <span className="bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                Complete Bali Coverage
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              From digital nomad hotspots to family-friendly neighborhoods and luxury destinations -
              we cover all of Bali's premier locations with comprehensive local expertise and market insights.
            </p>
          </div>

          {/* Premium Feature Cards */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Digital Nomad Locations */}
            <div className="group bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 border border-white/50 hover:border-blue-200/50">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors">Digital Nomad Hubs</h3>
              <p className="text-gray-600 mb-6 leading-relaxed">Perfect for remote workers and entrepreneurs seeking vibrant communities</p>
              <ul className="space-y-3">
                <li className="flex items-center text-gray-700">
                  <div className="w-3 h-3 bg-gradient-to-r from-emerald-400 to-teal-400 rounded-full mr-4"></div>
                  <span className="font-medium">Canggu</span> - Surf & coworking paradise
                </li>
                <li className="flex items-center text-gray-700">
                  <div className="w-3 h-3 bg-gradient-to-r from-emerald-400 to-teal-400 rounded-full mr-4"></div>
                  <span className="font-medium">Ubud</span> - Wellness & creativity center
                </li>
                <li className="flex items-center text-gray-700">
                  <div className="w-3 h-3 bg-gradient-to-r from-emerald-400 to-teal-400 rounded-full mr-4"></div>
                  <span className="font-medium">Seminyak</span> - Luxury nomad lifestyle
                </li>
              </ul>
            </div>

            {/* Family Locations */}
            <div className="group bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 border border-white/50 hover:border-green-200/50">
              <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-green-600 transition-colors">Family Destinations</h3>
              <p className="text-gray-600 mb-6 leading-relaxed">Safe neighborhoods with international schools and family amenities</p>
              <ul className="space-y-3">
                <li className="flex items-center text-gray-700">
                  <div className="w-3 h-3 bg-gradient-to-r from-emerald-400 to-teal-400 rounded-full mr-4"></div>
                  <span className="font-medium">Sanur</span> - Traditional & family-friendly
                </li>
                <li className="flex items-center text-gray-700">
                  <div className="w-3 h-3 bg-gradient-to-r from-emerald-400 to-teal-400 rounded-full mr-4"></div>
                  <span className="font-medium">Jimbaran</span> - Luxury family living
                </li>
                <li className="flex items-center text-gray-700">
                  <div className="w-3 h-3 bg-gradient-to-r from-emerald-400 to-teal-400 rounded-full mr-4"></div>
                  <span className="font-medium">Seminyak</span> - Upscale family amenities
                </li>
              </ul>
            </div>

            {/* Investment Opportunities */}
            <div className="group bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 border border-white/50 hover:border-purple-200/50 md:col-span-2 lg:col-span-1">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-purple-600 transition-colors">Investment Hotspots</h3>
              <p className="text-gray-600 mb-6 leading-relaxed">High-growth areas with strong ROI potential and market insights</p>
              <ul className="space-y-3">
                <li className="flex items-center text-gray-700">
                  <div className="w-3 h-3 bg-gradient-to-r from-emerald-400 to-teal-400 rounded-full mr-4"></div>
                  <span className="font-medium">All locations</span> - Comprehensive analysis
                </li>
                <li className="flex items-center text-gray-700">
                  <div className="w-3 h-3 bg-gradient-to-r from-emerald-400 to-teal-400 rounded-full mr-4"></div>
                  <span className="font-medium">Market data</span> & rental yields
                </li>
                <li className="flex items-center text-gray-700">
                  <div className="w-3 h-3 bg-gradient-to-r from-emerald-400 to-teal-400 rounded-full mr-4"></div>
                  <span className="font-medium">Legal guidance</span> & compliance
                </li>
              </ul>
            </div>
          </div>

          {/* Premium CTA */}
          <div className="text-center mt-16">
            <Link
              href="/locations"
              className="group inline-flex items-center bg-gradient-to-r from-emerald-600 to-emerald-700 text-white px-10 py-5 rounded-2xl font-semibold text-lg shadow-xl hover:shadow-2xl hover:shadow-emerald-500/25 transition-all duration-300 transform hover:scale-105"
            >
              Explore All Locations
              <svg className="w-6 h-6 ml-3 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </Link>
          </div>
        </div>
      </section>

      {/* STORY-003: Advanced Schema Markup */}
      <AdvancedSchema type="organization" />
      <AdvancedSchema type="website" />
      <AdvancedSchema type="local-business" />
      <AdvancedSchema type="real-estate-agent" />

      {/* Chat Bubble */}
      <ChatBubble />
    </main>
  )
}

