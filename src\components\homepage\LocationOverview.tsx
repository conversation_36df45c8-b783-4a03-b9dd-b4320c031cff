'use client'

import Image from 'next/image'
import Link from 'next/link'
import { useState, useEffect } from 'react'

// Mock data - will be replaced with CMS data
const locations = [
  {
    id: 1,
    name: 'Canggu',
    slug: 'canggu',
    description: 'Vibrant surf town perfect for digital nomads with coworking spaces, trendy cafes, and beach lifestyle.',
    propertyCount: 120,
    priceRange: '$800-3000',
    image: '/images/locations/canggu-hero.jpg',
    priority: 'zeer-hoog'
  },
  {
    id: 2,
    name: 'Ubud',
    slug: 'ubud',
    description: 'Cultural heart of Bali surrounded by rice terraces, perfect for wellness and spiritual retreats.',
    propertyCount: 95,
    priceRange: '$600-2500',
    image: '/images/locations/ubud-hero.jpg',
    priority: 'zeer-hoog'
  },
  {
    id: 3,
    name: 'Seminyak',
    slug: 'seminyak',
    description: 'Upscale beach destination with luxury resorts, fine dining, and sophisticated nightlife.',
    propertyCount: 85,
    priceRange: '$1000-4000',
    image: '/images/locations/seminyak-hero.jpg',
    priority: 'hoog'
  },
  {
    id: 4,
    name: '<PERSON><PERSON>',
    slug: 'sanur',
    description: 'Family-friendly area with calm beaches, excellent schools, and established expat community.',
    propertyCount: 65,
    priceRange: '$700-2800',
    image: '/images/locations/sanur-hero.jpg',
    priority: 'middel'
  },
  {
    id: 5,
    name: 'Jimbaran',
    slug: 'jimbaran',
    description: 'Peaceful fishing village with beautiful bay, seafood restaurants, and luxury accommodations.',
    propertyCount: 45,
    priceRange: '$900-3500',
    image: '/images/locations/jimbaran-hero.jpg',
    priority: 'middel'
  },
  {
    id: 6,
    name: 'Pererenan',
    slug: 'pererenan',
    description: 'Emerging surf spot with rice field views, growing expat community, and authentic Balinese culture.',
    propertyCount: 35,
    priceRange: '$600-2200',
    image: '/images/locations/pererenan-hero.jpg',
    priority: 'middel'
  }
]

const LocationCard = ({ location, index }: { location: typeof locations[0], index: number }) => {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), index * 100)
    return () => clearTimeout(timer)
  }, [index])

  return (
    <div className={`group transition-all duration-700 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
      <Link href={`/locations/${location.slug}`} className="block">
        <div className="bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden">
          {/* Image */}
          <div className="relative h-48 overflow-hidden">
            <Image
              src={location.image}
              alt={`${location.name} - Beautiful location in Bali`}
              fill
              className="object-cover group-hover:scale-110 transition-transform duration-500"
              placeholder="blur"
              blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
            />
            {/* Priority Badge */}
            {location.priority === 'zeer-hoog' && (
              <div className="absolute top-3 left-3 bg-emerald-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                Popular
              </div>
            )}
          </div>

          {/* Content */}
          <div className="p-6">
            <div className="flex justify-between items-start mb-3">
              <h3 className="text-xl font-bold text-gray-900 group-hover:text-emerald-600 transition-colors">
                {location.name}
              </h3>
              <div className="text-right">
                <div className="text-sm text-gray-500">{location.propertyCount} properties</div>
                <div className="text-sm font-semibold text-emerald-600">{location.priceRange}/month</div>
              </div>
            </div>

            <p className="text-gray-600 text-sm leading-relaxed mb-4">
              {location.description}
            </p>

            {/* PRD Compliant CTAs */}
            <div className="flex flex-col gap-2 mt-4">
              <div className="flex items-center justify-between">
                <span className="text-emerald-600 font-semibold text-sm group-hover:text-emerald-700 transition-colors">
                  Learn More →
                </span>
                <div className="flex items-center gap-1">
                  {[...Array(5)].map((_, i) => (
                    <svg
                      key={i}
                      className={`w-4 h-4 ${i < 4 ? 'text-yellow-400' : 'text-gray-300'}`}
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </Link>

      {/* PRD Compliant Find Properties Button */}
      <div className="mt-3 px-6 pb-4">
        <Link
          href={`https://app.balipropertyscout.com?query=rent-villa-in-${location.slug}`}
          className="block w-full text-center bg-emerald-100 hover:bg-emerald-200 text-emerald-700 px-4 py-2 rounded-lg font-medium text-sm transition-colors"
        >
          Find Properties in {location.name}
        </Link>
      </div>
    </div>
  )
}

const LocationOverview = () => {
  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
            Explore Popular Locations
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Discover the perfect area for your lifestyle. From vibrant surf towns to cultural centers, 
            each location offers unique experiences for expats and digital nomads.
          </p>
        </div>

        {/* Location Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {locations.map((location, index) => (
            <LocationCard key={location.id} location={location} index={index} />
          ))}
        </div>

        {/* View All CTA */}
        <div className="text-center">
          <Link
            href="/locations"
            className="inline-flex items-center gap-2 bg-emerald-600 hover:bg-emerald-700 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
          >
            View All Locations
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  )
}

export default LocationOverview
