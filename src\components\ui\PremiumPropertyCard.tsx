'use client';

import { motion } from '@/components/ui/SimpleMotion';
import Link from 'next/link';
import { useState } from 'react';

interface PremiumPropertyCardProps {
  title: string;
  description: string;
  location: string;
  priceRange: string;
  priority: 'ZEER HOOG' | 'HOOG' | 'GEMIDDELD';
  href: string;
  image?: string;
  stats?: {
    bedrooms?: number;
    bathrooms?: number;
    area?: string;
  };
  features?: string[];
  className?: string;
}

const priorityConfig = {
  'ZEER HOOG': {
    badge: 'bg-gradient-to-r from-red-500 to-pink-500 text-white',
    glow: 'group-hover:shadow-red-500/25',
    border: 'border-red-200/50',
    accent: 'text-red-600'
  },
  'HOOG': {
    badge: 'bg-gradient-to-r from-orange-500 to-amber-500 text-white',
    glow: 'group-hover:shadow-orange-500/25',
    border: 'border-orange-200/50',
    accent: 'text-orange-600'
  },
  'GEMIDDELD': {
    badge: 'bg-gradient-to-r from-emerald-500 to-emerald-600 text-white',
    glow: 'group-hover:shadow-emerald-500/25',
    border: 'border-emerald-200/50',
    accent: 'text-emerald-600'
  }
};

export function PremiumPropertyCard({
  title,
  description,
  location,
  priceRange,
  priority,
  href,
  image,
  stats,
  features = [],
  className = ''
}: PremiumPropertyCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  // Calculate rotation based on mouse position
  const rotateX = (mousePosition.y * 20) - 10; // -10 to 10 degrees
  const rotateY = (mousePosition.x * 20) - 10; // -10 to 10 degrees

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;

    const x = (e.clientX - centerX) / rect.width;
    const y = (e.clientY - centerY) / rect.height;

    setMousePosition({ x, y });
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    setMousePosition({ x: 0, y: 0 });
  };

  const config = priorityConfig[priority];

  return (
    <motion.div
      className={`group relative ${className}`}
      style={{
        perspective: '1000px',
        transformStyle: 'preserve-3d'
      }}
      onMouseMove={handleMouseMove}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={handleMouseLeave}
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
    >
      <Link href={href} className="block">
        <motion.div
          className={`
            relative bg-white rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-500 overflow-hidden
            ${config.glow} ${config.border} border backdrop-blur-sm
            transform-gpu
          `}
          style={{
            transform: `rotateX(${rotateX}deg) rotateY(${rotateY}deg)`,
            transformStyle: 'preserve-3d',
            transition: 'transform 0.1s ease-out'
          }}
        >
          {/* Priority Badge */}
          <div className="absolute top-6 left-6 z-20">
            <motion.div
              className={`px-4 py-2 rounded-full text-sm font-bold shadow-lg ${config.badge}`}
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.2 }}
            >
              {priority}
            </motion.div>
          </div>

          {/* Price Badge */}
          <div className="absolute top-6 right-6 z-20">
            <motion.div
              className="px-4 py-2 rounded-full text-sm font-bold bg-black/80 text-white backdrop-blur-sm"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.2 }}
            >
              {priceRange}
            </motion.div>
          </div>

          {/* Image Section with Parallax */}
          <div className="relative h-64 overflow-hidden rounded-t-3xl">
            {image ? (
              <motion.div
                className="w-full h-full bg-cover bg-center"
                style={{
                  backgroundImage: `url(${image})`,
                  scale: isHovered ? 1.1 : 1
                }}
                transition={{ duration: 0.6, ease: "easeOut" }}
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200" />
            )}
            
            {/* Gradient Overlay */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
            
            {/* Location Badge */}
            <div className="absolute bottom-4 left-6">
              <div className="flex items-center space-x-2 text-white">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                </svg>
                <span className="text-sm font-medium">{location}</span>
              </div>
            </div>
          </div>

          {/* Content Section */}
          <div className="p-8">
            {/* Title */}
            <motion.h3
              className={`text-2xl font-bold text-gray-900 mb-3 group-hover:${config.accent} transition-colors duration-300`}
              style={{ transform: 'translateZ(20px)' }}
            >
              {title}
            </motion.h3>

            {/* Description */}
            <p className="text-gray-600 mb-6 leading-relaxed">
              {description}
            </p>

            {/* Stats */}
            {stats && (
              <div className="flex items-center space-x-6 mb-6 text-sm text-gray-500">
                {stats.bedrooms && (
                  <div className="flex items-center space-x-1">
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"/>
                    </svg>
                    <span>{stats.bedrooms} bed</span>
                  </div>
                )}
                {stats.bathrooms && (
                  <div className="flex items-center space-x-1">
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                    </svg>
                    <span>{stats.bathrooms} bath</span>
                  </div>
                )}
                {stats.area && (
                  <div className="flex items-center space-x-1">
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                    </svg>
                    <span>{stats.area}</span>
                  </div>
                )}
              </div>
            )}

            {/* Features */}
            {features.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-6">
                {features.slice(0, 3).map((feature, index) => (
                  <span
                    key={index}
                    className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-xs font-medium"
                  >
                    {feature}
                  </span>
                ))}
                {features.length > 3 && (
                  <span className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-xs font-medium">
                    +{features.length - 3} more
                  </span>
                )}
              </div>
            )}

            {/* CTA */}
            <div className="flex items-center justify-between">
              <span className={`font-semibold ${config.accent} group-hover:underline transition-all duration-300`}>
                View Details
              </span>
              
              <motion.div
                className={`w-12 h-12 rounded-full flex items-center justify-center transition-all duration-300 ${config.accent.replace('text-', 'bg-').replace('600', '100')} group-hover:${config.accent.replace('text-', 'bg-').replace('600', '200')}`}
                whileHover={{ x: 4, scale: 1.1 }}
                transition={{ duration: 0.2 }}
                style={{ transform: 'translateZ(30px)' }}
              >
                <svg className={`w-5 h-5 ${config.accent}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </motion.div>
            </div>
          </div>

          {/* Hover Glow Effect */}
          <motion.div
            className="absolute inset-0 rounded-3xl bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"
            style={{ transform: 'translateZ(10px)' }}
          />
        </motion.div>
      </Link>
    </motion.div>
  );
}

export default PremiumPropertyCard;
