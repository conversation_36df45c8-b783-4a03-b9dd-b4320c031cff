import { getPayload } from 'payload'
import config from '../../payload.config'

const addApartmentPropertyType = async (): Promise<void> => {
  try {
    const payload = await getPayload({ config })

    console.log('🏢 Adding Apartment property type...')

    // Check if apartment already exists
    const existing = await payload.find({
      collection: 'property-types',
      where: {
        slug: {
          equals: 'apartment'
        }
      }
    })

    if (existing.docs.length > 0) {
      console.log('ℹ️ Apartment property type already exists')
      return
    }

    // Create comprehensive apartment property type
    const apartmentData = {
      name: 'Apartment',
      slug: 'apartment',
      category: 'both',
      description: {
        root: {
          type: 'root',
          children: [
            {
              type: 'paragraph',
              children: [
                {
                  type: 'text',
                  text: 'Modern apartments in Bali offer the perfect blend of contemporary living and tropical lifestyle. From sleek studio units to spacious penthouses, these properties provide convenience, security, and often stunning views of the ocean, rice fields, or bustling cityscapes.'
                }
              ]
            }
          ]
        }
      },
      detailedDescription: {
        root: {
          type: 'root',
          children: [
            {
              type: 'paragraph',
              children: [
                {
                  type: 'text',
                  text: 'Apartment living in Bali has evolved significantly over the past decade, with developers creating sophisticated residential complexes that cater to both local and international residents. These modern developments typically feature high-quality construction, contemporary design, and comprehensive amenities that rival luxury hotels. Most apartment complexes in Bali are strategically located in prime areas such as Seminyak, Canggu, Sanur, and Ubud, offering residents easy access to beaches, restaurants, shopping centers, and cultural attractions. The apartment market in Bali serves diverse needs, from compact studios perfect for digital nomads and young professionals to expansive three-bedroom units ideal for families and long-term residents.'
                }
              ]
            }
          ]
        }
      },
      typicalFeatures: [
        { feature: 'Air conditioning throughout', description: 'Central or split-system AC in all rooms' },
        { feature: 'Modern kitchen appliances', description: 'Refrigerator, stove, microwave, and dishwasher' },
        { feature: 'High-speed WiFi', description: 'Fiber optic internet connection included' },
        { feature: 'Swimming pool access', description: 'Shared pool facilities with sun deck' },
        { feature: '24/7 security', description: 'Professional security staff and CCTV monitoring' },
        { feature: 'Gym facilities', description: 'Fully equipped fitness center' },
        { feature: 'Parking space', description: 'Covered parking for car and motorbike' },
        { feature: 'Balcony or terrace', description: 'Private outdoor space with views' },
        { feature: 'Housekeeping service', description: 'Weekly or bi-weekly cleaning service' },
        { feature: 'Concierge services', description: 'Assistance with bookings and local information' }
      ],
      priceRanges: {
        rental: {
          shortTerm: '$40-150/night',
          longTerm: '$600-2500/month'
        },
        purchase: {
          freehold: '$80k-500k',
          leasehold: '$50k-300k'
        }
      },
      sizeRanges: {
        bedroomsMin: 0,
        bedroomsMax: 3,
        areaMin: 25,
        areaMax: 150
      },
      targetAudience: [
        { audience: 'digital-nomads' },
        { audience: 'expat-families' },
        { audience: 'property-investors' },
        { audience: 'budget-travelers' }
      ],
      rentalTerms: {
        root: {
          type: 'root',
          children: [
            {
              type: 'paragraph',
              children: [
                {
                  type: 'text',
                  text: 'Apartment rentals in Bali typically require a security deposit equivalent to 1-2 months rent. Most properties include utilities such as water, electricity (up to a certain limit), internet, and access to shared facilities. Long-term rentals (6+ months) often receive discounted rates and may include additional services such as housekeeping and maintenance. Short-term rentals are fully furnished and include all amenities, perfect for vacation stays or business trips.'
                }
              ]
            }
          ]
        }
      },
      purchaseProcess: {
        root: {
          type: 'root',
          children: [
            {
              type: 'paragraph',
              children: [
                {
                  type: 'text',
                  text: 'Purchasing an apartment in Bali involves several legal considerations. Foreign buyers can acquire apartments through leasehold arrangements (25-30 years, renewable) or through Indonesian entities for freehold ownership. The process includes due diligence on the developer, review of building permits and certificates, legal documentation review, and proper transfer procedures. Most modern apartment complexes offer professional management services, making them attractive investment properties with potential rental yields of 6-10% annually.'
                }
              ]
            }
          ]
        }
      },
      investmentAnalysis: {
        rentalYield: '6-10% annually',
        appreciationRate: '5-8% per year',
        investmentHighlights: [
          { highlight: 'Lower maintenance compared to villas' },
          { highlight: 'Professional property management available' },
          { highlight: 'High rental demand from tourists and expats' },
          { highlight: 'Modern amenities attract premium tenants' },
          { highlight: 'Strategic locations with good accessibility' },
          { highlight: 'Potential for capital appreciation in prime areas' }
        ]
      },
      faq: [
        {
          question: 'Can foreigners buy apartments in Bali?',
          answer: {
            root: {
              type: 'root',
              children: [
                {
                  type: 'paragraph',
                  children: [
                    {
                      type: 'text',
                      text: 'Yes, foreigners can purchase apartments in Bali through leasehold arrangements (25-30 years, renewable) or by establishing an Indonesian legal entity (PT PMA) for freehold ownership. Many developers offer assistance with the legal process.'
                    }
                  ]
                }
              ]
            }
          }
        },
        {
          question: 'What are the typical monthly costs for apartment living?',
          answer: {
            root: {
              type: 'root',
              children: [
                {
                  type: 'paragraph',
                  children: [
                    {
                      type: 'text',
                      text: 'Monthly costs typically include rent ($600-2500), utilities ($50-150), internet ($25-50), and optional services like housekeeping ($100-200). Many apartments include basic utilities and amenities in the rental price.'
                    }
                  ]
                }
              ]
            }
          }
        },
        {
          question: 'Are apartments suitable for families with children?',
          answer: {
            root: {
              type: 'root',
              children: [
                {
                  type: 'paragraph',
                  children: [
                    {
                      type: 'text',
                      text: 'Yes, many apartment complexes are family-friendly with playgrounds, kids pools, and proximity to international schools. Two and three-bedroom units provide ample space for families, and the security and amenities make them very suitable for children.'
                    }
                  ]
                }
              ]
            }
          }
        }
      ]
    }

    // Create the apartment property type
    const created = await payload.create({
      collection: 'property-types',
      data: apartmentData,
    })

    console.log(`✅ Created apartment property type: ${created.id}`)
    console.log('🎉 Apartment property type added successfully!')
    
  } catch (error) {
    console.error('❌ Error adding apartment property type:', error)
    throw error
  }
}

// Run if called directly
if (require.main === module) {
  addApartmentPropertyType()
    .then(() => {
      console.log('✅ Script completed successfully')
      process.exit(0)
    })
    .catch((error) => {
      console.error('❌ Script failed:', error)
      process.exit(1)
    })
}

export { addApartmentPropertyType }
