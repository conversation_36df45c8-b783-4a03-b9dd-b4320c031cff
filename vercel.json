{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "installCommand": "npm install", "env": {"SKIP_ENV_VALIDATION": "true"}, "build": {"env": {"SKIP_ENV_VALIDATION": "true", "NODE_ENV": "production"}}, "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/((?!api|_next/static|_next/image|favicon.ico).*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "origin-when-cross-origin"}]}, {"source": "/sw.js", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}]}, {"source": "/:path*\\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "rewrites": [{"source": "/sitemap.xml", "destination": "/sitemap"}]}