'use client'

import { useEffect } from 'react'
import { useAIContext } from './AIContext'

interface AIContextSetterProps {
  location?: string
  propertyType?: string
  page?: string
}

export const AIContextSetter: React.FC<AIContextSetterProps> = ({
  location,
  propertyType,
  page = 'unknown'
}) => {
  const { dispatch } = useAIContext()

  useEffect(() => {
    dispatch({
      type: 'SET_CONTEXT',
      context: {
        location,
        propertyType,
        page
      }
    })
  }, [location, propertyType, page, dispatch])

  // This component doesn't render anything
  return null
}
