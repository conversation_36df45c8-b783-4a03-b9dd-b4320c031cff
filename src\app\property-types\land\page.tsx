/**
 * Land Property Type Page
 * PRD Requirement: Create land page marked as "to be built"
 */

import { Metadata } from 'next';
import Link from 'next/link';
import { Breadcrumbs } from '@/components/common/Breadcrumbs';

export const metadata: Metadata = {
  title: 'Land for Sale in Bali - Investment Opportunities | Bali Real Estate',
  description: 'Invest in land to build your own dream home or development project. Purchase: $50,000-200,000 per are. Perfect for developers and long-term investors. Foreign ownership restrictions apply.',
  keywords: 'buying land in bali, bali land investment, land for sale bali, bali property development, leasehold land bali',
};

export default function LandPage() {
  return (
    <main className="min-h-screen bg-gray-50">
      {/* Breadcrumbs */}
      <div className="bg-white py-4">
        <div className="max-w-6xl mx-auto px-4">
          <Breadcrumbs />
        </div>
      </div>

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-green-600 to-emerald-600 text-white py-20">
        <div className="max-w-6xl mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                Land for Sale in Bali
              </h1>
              <p className="text-xl mb-8 text-green-100 leading-relaxed">
                Invest in land to build your own dream home or development project.
              </p>
              
              {/* Key Stats */}
              <div className="grid grid-cols-2 gap-4 mb-8">
                <div className="bg-white/10 rounded-lg p-4">
                  <div className="text-2xl font-bold text-white">$50K-200K</div>
                  <div className="text-green-200 text-sm">Per Are (100m²)</div>
                </div>
                <div className="bg-white/10 rounded-lg p-4">
                  <div className="text-2xl font-bold text-white">Leasehold</div>
                  <div className="text-green-200 text-sm">25-30 Years</div>
                </div>
              </div>

              {/* Perfect For */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold mb-3 text-green-100">Perfect for:</h3>
                <div className="flex flex-wrap gap-2">
                  <span className="bg-white/20 px-3 py-1 rounded-full text-sm">Developers</span>
                  <span className="bg-white/20 px-3 py-1 rounded-full text-sm">Long-term Investors</span>
                  <span className="bg-white/20 px-3 py-1 rounded-full text-sm">Custom Builds</span>
                </div>
              </div>

              {/* Important Notice */}
              <div className="bg-yellow-500/20 border border-yellow-400/30 rounded-lg p-4 mb-8">
                <div className="flex items-start">
                  <svg className="w-5 h-5 text-yellow-300 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  <div>
                    <p className="text-yellow-100 text-sm">
                      <strong>Important:</strong> Foreign ownership of land is restricted. 
                      Legal consultation required for all land purchases.
                    </p>
                  </div>
                </div>
              </div>

              {/* PRD Compliant CTAs */}
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  href="https://app.balipropertyscout.com?query=buy-land-in-bali"
                  className="bg-white text-green-600 px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl text-center"
                >
                  Find Land
                </Link>
                <Link
                  href="/services"
                  className="bg-white/10 hover:bg-white/20 text-white border-2 border-white/30 hover:border-white/50 px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 backdrop-blur-sm text-center"
                >
                  Legal Services
                </Link>
              </div>
            </div>

            <div className="relative">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
                <h3 className="text-xl font-bold mb-4">Coming Soon</h3>
                <p className="text-green-100 mb-6">
                  We're currently developing our land listings and legal guidance resources. 
                  Our chatbot can help you with immediate land investment inquiries.
                </p>
                <div className="space-y-3">
                  <div className="flex items-center text-green-100">
                    <svg className="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    Prime locations
                  </div>
                  <div className="flex items-center text-green-100">
                    <svg className="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    Legal compliance
                  </div>
                  <div className="flex items-center text-green-100">
                    <svg className="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    Development potential
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Under Construction Notice */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <div className="bg-white rounded-lg shadow-lg p-12">
            <div className="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-8">
              <svg className="w-12 h-12 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
            </div>
            
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Land Listings Coming Soon
            </h2>
            
            <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
              We're working hard to bring you comprehensive land listings and legal guidance across Bali. 
              In the meantime, our AI assistant can help you with land investment inquiries and connect you with legal services.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Link
                href="https://app.balipropertyscout.com?query=buy-land-in-bali"
                className="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
              >
                Find Land Now
              </Link>
              <Link
                href="/contact"
                className="border-2 border-green-600 text-green-600 hover:bg-green-600 hover:text-white px-8 py-3 rounded-lg font-semibold transition-colors"
              >
                Contact Our Team
              </Link>
            </div>

            <div className="text-gray-500 text-sm">
              <p>Available property types: <Link href="/property-types/villa" className="text-green-600 hover:underline">Villas</Link> • <Link href="/property-types/guesthouse" className="text-green-600 hover:underline">Guesthouses</Link></p>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}
