import { NextRequest, NextResponse } from 'next/server'
import { getPayloadHMR } from '@payloadcms/next/utilities'
import configPromise from '../../../../../payload.config'

// GET /api/ai/preferences - Get user preferences by session
export async function GET(request: NextRequest) {
  try {
    const payload = await getPayloadHMR({ config: configPromise })
    const { searchParams } = new URL(request.url)
    const sessionId = searchParams.get('sessionId')

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      )
    }

    // Find conversation by session ID
    const conversations = await payload.find({
      collection: 'ai-conversations',
      where: {
        sessionId: {
          equals: sessionId,
        },
        status: {
          not_equals: 'expired',
        },
      },
      limit: 1,
    })

    if (conversations.docs.length === 0) {
      return NextResponse.json(
        { preferences: null, exists: false },
        { status: 200 }
      )
    }

    const conversation = conversations.docs[0]
    
    return NextResponse.json({
      preferences: conversation.userPreferences,
      exists: true,
    })

  } catch (error) {
    console.error('Error retrieving preferences:', error)
    return NextResponse.json(
      { error: 'Failed to retrieve preferences' },
      { status: 500 }
    )
  }
}

// POST /api/ai/preferences - Update user preferences
export async function POST(request: NextRequest) {
  try {
    const payload = await getPayloadHMR({ config: configPromise })
    const body = await request.json()
    
    const { sessionId, preferences } = body

    if (!sessionId || !preferences) {
      return NextResponse.json(
        { error: 'Session ID and preferences are required' },
        { status: 400 }
      )
    }

    // Find conversation by session ID
    const conversations = await payload.find({
      collection: 'ai-conversations',
      where: {
        sessionId: {
          equals: sessionId,
        },
        status: {
          not_equals: 'expired',
        },
      },
      limit: 1,
    })

    if (conversations.docs.length === 0) {
      return NextResponse.json(
        { error: 'Conversation not found' },
        { status: 404 }
      )
    }

    const conversation = conversations.docs[0]

    // Merge new preferences with existing ones
    const updatedPreferences = {
      ...conversation.userPreferences,
      ...preferences,
    }

    // Update conversation with new preferences
    const updatedConversation = await payload.update({
      collection: 'ai-conversations',
      id: conversation.id,
      data: {
        userPreferences: updatedPreferences,
      },
    })

    return NextResponse.json({
      preferences: updatedConversation.userPreferences,
      success: true,
    })

  } catch (error) {
    console.error('Error updating preferences:', error)
    return NextResponse.json(
      { error: 'Failed to update preferences' },
      { status: 500 }
    )
  }
}
