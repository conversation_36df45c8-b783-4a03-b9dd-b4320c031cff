import { getPayload } from 'payload'
import config from '../../payload.config'

let payload: any = null

export const getPayloadClient = async () => {
  if (!payload) {
    payload = await getPayload({ config })
  }
  return payload
}

export const getLocations = async () => {
  // Check if we're in build environment or database is not available
  const isBuildTime = process.env.VERCEL_ENV && !process.env.DATABASE_URI;

  if (isBuildTime) {
    // Return static locations for build-time
    return [
      { slug: 'canggu', name: 'Canggu', updatedAt: new Date().toISOString() },
      { slug: 'ubud', name: 'Ubud', updatedAt: new Date().toISOString() },
      { slug: 'seminyak', name: 'Seminyak', updatedAt: new Date().toISOString() },
      { slug: 'sanur', name: 'Sanur', updatedAt: new Date().toISOString() },
      { slug: 'jimba<PERSON>', name: '<PERSON><PERSON><PERSON>', updatedAt: new Date().toISOString() },
    ]
  }

  try {
    // Add timeout protection (increased for development)
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Database timeout after 15 seconds')), 15000)
    })

    const payload = await getPayloadClient()
    const locationsPromise = payload.find({
      collection: 'locations',
      limit: 100,
    })

    const locations = await Promise.race([locationsPromise, timeoutPromise])
    return (locations as any).docs
  } catch (error) {
    // Silently fall back to static data in development
    if (process.env.NODE_ENV === 'development') {
      console.log('📊 Using static locations (database not connected)')
    } else {
      console.error('Database not available, using static locations:', error)
    }
    // Fallback to static locations
    return [
      { slug: 'canggu', name: 'Canggu', updatedAt: new Date().toISOString() },
      { slug: 'ubud', name: 'Ubud', updatedAt: new Date().toISOString() },
      { slug: 'seminyak', name: 'Seminyak', updatedAt: new Date().toISOString() },
      { slug: 'sanur', name: 'Sanur', updatedAt: new Date().toISOString() },
      { slug: 'jimbaran', name: 'Jimbaran', updatedAt: new Date().toISOString() },
    ]
  }
}

export const getLocationBySlug = async (slug: string) => {
  // Check if we're in build environment or database is not available
  const isBuildTime = process.env.VERCEL_ENV && !process.env.DATABASE_URI;

  if (isBuildTime) {
    // Return static location data for build-time
    const staticLocations: any = {
      'canggu': { slug: 'canggu', name: 'Canggu', updatedAt: new Date().toISOString() },
      'ubud': { slug: 'ubud', name: 'Ubud', updatedAt: new Date().toISOString() },
      'seminyak': { slug: 'seminyak', name: 'Seminyak', updatedAt: new Date().toISOString() },
      'sanur': { slug: 'sanur', name: 'Sanur', updatedAt: new Date().toISOString() },
      'jimbaran': { slug: 'jimbaran', name: 'Jimbaran', updatedAt: new Date().toISOString() },
    }
    return staticLocations[slug] || null
  }

  try {
    const payload = await getPayloadClient()
    const locations = await payload.find({
      collection: 'locations',
      where: {
        slug: {
          equals: slug,
        },
      },
      limit: 1,
    })
    return locations.docs[0] || null
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.log('📊 Using static location data (database not connected)')
    } else {
      console.error('Database not available, using static location data:', error)
    }
    // Fallback to static location data
    const staticLocations: any = {
      'canggu': { slug: 'canggu', name: 'Canggu', updatedAt: new Date().toISOString() },
      'ubud': { slug: 'ubud', name: 'Ubud', updatedAt: new Date().toISOString() },
      'seminyak': { slug: 'seminyak', name: 'Seminyak', updatedAt: new Date().toISOString() },
      'sanur': { slug: 'sanur', name: 'Sanur', updatedAt: new Date().toISOString() },
      'jimbaran': { slug: 'jimbaran', name: 'Jimbaran', updatedAt: new Date().toISOString() },
    }
    return staticLocations[slug] || null
  }
}

export const getPropertyTypes = async () => {
  // Check if we're in build environment or database is not available
  const isBuildTime = process.env.VERCEL_ENV && !process.env.DATABASE_URI;

  if (isBuildTime) {
    // Return static property types for build-time
    return [
      { slug: 'villa', name: 'Villa', updatedAt: new Date().toISOString() },
      { slug: 'apartment', name: 'Apartment', updatedAt: new Date().toISOString() },
      { slug: 'guesthouse', name: 'Guesthouse', updatedAt: new Date().toISOString() },
      { slug: 'land', name: 'Land', updatedAt: new Date().toISOString() },
    ]
  }

  try {
    const payload = await getPayloadClient()
    const propertyTypes = await payload.find({
      collection: 'property-types',
      limit: 100,
    })
    return propertyTypes.docs
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.log('📊 Using static property types (database not connected)')
    } else {
      console.error('Database not available, using static property types:', error)
    }
    // Fallback to static property types
    return [
      { slug: 'villa', name: 'Villa', updatedAt: new Date().toISOString() },
      { slug: 'apartment', name: 'Apartment', updatedAt: new Date().toISOString() },
      { slug: 'guesthouse', name: 'Guesthouse', updatedAt: new Date().toISOString() },
      { slug: 'land', name: 'Land', updatedAt: new Date().toISOString() },
    ]
  }
}

export const getPropertyTypeBySlug = async (slug: string) => {
  const payload = await getPayloadClient()
  const propertyTypes = await payload.find({
    collection: 'property-types',
    where: {
      slug: {
        equals: slug,
      },
    },
    limit: 1,
  })
  return propertyTypes.docs[0] || null
}
