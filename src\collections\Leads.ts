import { CollectionConfig } from 'payload'

export const Leads: CollectionConfig = {
  slug: 'leads',
  admin: {
    useAsTitle: 'email',
    defaultColumns: ['name', 'email', 'leadType', 'leadScore', 'status', 'createdAt'],
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      label: 'Full Name',
    },
    {
      name: 'email',
      type: 'email',
      required: true,
      unique: true,
      label: 'Email Address',
    },
    {
      name: 'phone',
      type: 'text',
      label: 'Phone Number',
    },
    {
      name: 'leadType',
      type: 'select',
      required: true,
      options: [
        { label: 'General Inquiry', value: 'general' },
        { label: 'Property Interest', value: 'property-interest' },
        { label: 'Consultation Request', value: 'consultation' },
        { label: 'Newsletter Signup', value: 'newsletter' },
      ],
      label: 'Lead Type',
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      options: [
        { label: 'New', value: 'new' },
        { label: 'Contacted', value: 'contacted' },
        { label: 'Qualified', value: 'qualified' },
        { label: 'Converted', value: 'converted' },
        { label: 'Closed', value: 'closed' },
      ],
      defaultValue: 'new',
    },
    {
      name: 'leadScore',
      type: 'number',
      min: 0,
      max: 50,
      defaultValue: 0,
      label: 'Lead Score',
      admin: {
        description: 'Calculated score based on budget, timeline, specificity (max 50)',
      },
    },
    {
      name: 'source',
      type: 'select',
      options: [
        { label: 'Website Contact Form', value: 'contact-form' },
        { label: 'Location Page', value: 'location-page' },
        { label: 'Property Type Page', value: 'property-page' },
        { label: 'Educational Content', value: 'content-page' },
        { label: 'Newsletter', value: 'newsletter' },
        { label: 'Social Media', value: 'social-media' },
        { label: 'Referral', value: 'referral' },
        { label: 'Direct', value: 'direct' },
      ],
      label: 'Lead Source',
    },
    {
      name: 'budget',
      type: 'group',
      label: 'Budget Information',
      fields: [
        {
          name: 'budgetRange',
          type: 'select',
          options: [
            { label: 'Under $500/month', value: 'under-500' },
            { label: '$500-1000/month', value: '500-1000' },
            { label: '$1000-2000/month', value: '1000-2000' },
            { label: '$2000-3000/month', value: '2000-3000' },
            { label: 'Over $3000/month', value: 'over-3000' },
            { label: 'Under $100k purchase', value: 'under-100k' },
            { label: '$100k-300k purchase', value: '100k-300k' },
            { label: '$300k-500k purchase', value: '300k-500k' },
            { label: 'Over $500k purchase', value: 'over-500k' },
          ],
          label: 'Budget Range',
        },
        {
          name: 'budgetFlexible',
          type: 'checkbox',
          label: 'Budget is Flexible',
        },
      ],
    },
    {
      name: 'timeline',
      type: 'select',
      options: [
        { label: 'Immediate (within 1 month)', value: 'immediate' },
        { label: 'Short-term (1-3 months)', value: 'short-term' },
        { label: 'Medium-term (3-6 months)', value: 'medium-term' },
        { label: 'Long-term (6+ months)', value: 'long-term' },
        { label: 'Just researching', value: 'research' },
      ],
      label: 'Timeline',
    },
    {
      name: 'propertyPreferences',
      type: 'group',
      label: 'Property Preferences',
      fields: [
        {
          name: 'propertyType',
          type: 'relationship',
          relationTo: 'property-types',
          hasMany: true,
          label: 'Interested Property Types',
        },
        {
          name: 'preferredLocations',
          type: 'relationship',
          relationTo: 'locations',
          hasMany: true,
          label: 'Preferred Locations',
        },
        {
          name: 'transactionType',
          type: 'select',
          options: [
            { label: 'Long-term Rental', value: 'rental' },
            { label: 'Property Purchase', value: 'purchase' },
            { label: 'Both', value: 'both' },
            { label: 'Undecided', value: 'undecided' },
          ],
          label: 'Transaction Type',
        },
      ],
    },
    {
      name: 'personalInfo',
      type: 'group',
      label: 'Personal Information',
      fields: [
        {
          name: 'currentLocation',
          type: 'text',
          label: 'Current Location/Country',
        },
        {
          name: 'occupation',
          type: 'select',
          options: [
            { label: 'Digital Nomad', value: 'digital-nomad' },
            { label: 'Remote Worker', value: 'remote-worker' },
            { label: 'Entrepreneur', value: 'entrepreneur' },
            { label: 'Retiree', value: 'retiree' },
            { label: 'Student', value: 'student' },
            { label: 'Family', value: 'family' },
            { label: 'Investor', value: 'investor' },
            { label: 'Other', value: 'other' },
          ],
          label: 'Occupation/Situation',
        },
        {
          name: 'familySize',
          type: 'number',
          label: 'Family/Group Size',
        },
        {
          name: 'previousBaliExperience',
          type: 'checkbox',
          label: 'Has Previous Bali Experience',
        },
      ],
    },
    {
      name: 'message',
      type: 'textarea',
      label: 'Original Message',
      admin: {
        description: 'Original inquiry message from the lead',
      },
    },
    {
      name: 'notes',
      type: 'array',
      label: 'Follow-up Notes',
      fields: [
        {
          name: 'note',
          type: 'textarea',
          required: true,
        },
        {
          name: 'author',
          type: 'text',
          defaultValue: 'System',
        },
        {
          name: 'date',
          type: 'date',
          defaultValue: () => new Date().toISOString(),
        },
      ],
    },
    {
      name: 'emailOptIn',
      type: 'checkbox',
      defaultValue: true,
      label: 'Email Marketing Opt-in',
    },
    {
      name: 'gdprConsent',
      type: 'checkbox',
      required: true,
      label: 'GDPR Consent Given',
    },
    {
      name: 'lastContactDate',
      type: 'date',
      label: 'Last Contact Date',
    },
    {
      name: 'nextFollowUpDate',
      type: 'date',
      label: 'Next Follow-up Date',
    },
  ],
  hooks: {
    beforeChange: [
      ({ data }) => {
        // Calculate lead score based on various factors
        let score = 0
        
        // Budget alignment (1-10 points)
        if (data.budget?.budgetRange) {
          const budgetScores = {
            'under-500': 2,
            '500-1000': 4,
            '1000-2000': 6,
            '2000-3000': 8,
            'over-3000': 10,
            'under-100k': 3,
            '100k-300k': 6,
            '300k-500k': 8,
            'over-500k': 10,
          }
          score += budgetScores[data.budget.budgetRange as keyof typeof budgetScores] || 0
        }
        
        // Timeline urgency (1-10 points)
        if (data.timeline) {
          const timelineScores = {
            'immediate': 10,
            'short-term': 8,
            'medium-term': 6,
            'long-term': 4,
            'research': 2,
          }
          score += timelineScores[data.timeline as keyof typeof timelineScores] || 0
        }
        
        // Location specificity (1-10 points)
        if (data.propertyPreferences?.preferredLocations?.length) {
          score += Math.min(data.propertyPreferences.preferredLocations.length * 3, 10)
        }
        
        // Property type clarity (1-10 points)
        if (data.propertyPreferences?.propertyType?.length) {
          score += Math.min(data.propertyPreferences.propertyType.length * 3, 10)
        }
        
        // Contact information completeness (1-10 points)
        let contactScore = 0
        if (data.name) contactScore += 2
        if (data.email) contactScore += 3
        if (data.phone) contactScore += 3
        if (data.personalInfo?.currentLocation) contactScore += 2
        score += contactScore
        
        data.leadScore = Math.min(score, 50) // Cap at 50
        
        return data
      },
    ],
  },
}
