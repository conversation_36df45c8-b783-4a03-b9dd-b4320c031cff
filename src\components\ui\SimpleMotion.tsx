'use client';

import React, { useEffect, useRef, useState } from 'react';

interface MotionProps {
  children: React.ReactNode;
  className?: string;
  initial?: {
    opacity?: number;
    y?: number;
    x?: number;
    scale?: number;
  };
  whileInView?: {
    opacity?: number;
    y?: number;
    x?: number;
    scale?: number;
  };
  transition?: {
    duration?: number;
    delay?: number;
  };
  viewport?: {
    once?: boolean;
  };
  style?: React.CSSProperties;
  onClick?: () => void;
  animate?: {
    opacity?: number;
    y?: number;
    x?: number;
    scale?: number;
    rotate?: number;
  };
  whileHover?: {
    scale?: number;
    y?: number;
  };
  whileTap?: {
    scale?: number;
  };
}

export function SimpleMotion({ 
  children, 
  className = '', 
  initial = {}, 
  whileInView = {}, 
  transition = {},
  viewport = { once: true },
  style = {},
  onClick,
  animate = {},
  whileHover = {},
  whileTap = {}
}: MotionProps) {
  const ref = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [isPressed, setIsPressed] = useState(false);

  useEffect(() => {
    const element = ref.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          if (viewport.once) {
            observer.unobserve(element);
          }
        } else if (!viewport.once) {
          setIsVisible(false);
        }
      },
      { threshold: 0.1 }
    );

    observer.observe(element);

    return () => observer.disconnect();
  }, [viewport.once]);

  const getTransform = () => {
    let transform = '';
    
    // Determine current state values
    const currentState = isVisible ? whileInView : initial;
    
    // Apply hover effects
    if (isHovered && whileHover.scale) {
      transform += `scale(${whileHover.scale}) `;
    } else if (currentState.scale !== undefined) {
      transform += `scale(${currentState.scale}) `;
    }
    
    // Apply tap effects
    if (isPressed && whileTap.scale) {
      transform += `scale(${whileTap.scale}) `;
    }
    
    // Apply translations
    if (isHovered && whileHover.y) {
      transform += `translateY(${whileHover.y}px) `;
    } else if (currentState.y !== undefined) {
      transform += `translateY(${currentState.y}px) `;
    }
    
    if (currentState.x !== undefined) {
      transform += `translateX(${currentState.x}px) `;
    }

    // Apply animate values if provided
    if (animate.rotate !== undefined) {
      transform += `rotate(${animate.rotate}deg) `;
    }
    
    return transform.trim();
  };

  const getOpacity = () => {
    if (isVisible && whileInView.opacity !== undefined) {
      return whileInView.opacity;
    }
    if (animate.opacity !== undefined) {
      return animate.opacity;
    }
    return initial.opacity !== undefined ? initial.opacity : 1;
  };

  const duration = transition.duration || 0.6;
  const delay = transition.delay || 0;

  const combinedStyle: React.CSSProperties = {
    ...style,
    opacity: getOpacity(),
    transform: getTransform(),
    transition: `all ${duration}s ease-out ${delay}s`,
  };

  return (
    <div
      ref={ref}
      className={className}
      style={combinedStyle}
      onClick={onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onMouseDown={() => setIsPressed(true)}
      onMouseUp={() => setIsPressed(false)}
      onMouseLeave={() => setIsPressed(false)}
    >
      {children}
    </div>
  );
}

// Export as motion for easy replacement
export const motion = {
  div: SimpleMotion,
  section: SimpleMotion,
  article: SimpleMotion,
  header: SimpleMotion,
  main: SimpleMotion,
  aside: SimpleMotion,
  footer: SimpleMotion,
  nav: SimpleMotion,
  ul: SimpleMotion,
  li: SimpleMotion,
  h1: SimpleMotion,
  h2: SimpleMotion,
  h3: SimpleMotion,
  h4: SimpleMotion,
  h5: SimpleMotion,
  h6: SimpleMotion,
  p: SimpleMotion,
  span: SimpleMotion,
  button: SimpleMotion,
};

// Placeholder hooks for compatibility
export const useScroll = () => ({ scrollY: { get: () => 0 } });
export const useTransform = () => 0;
export const useMotionValue = () => ({ get: () => 0, set: () => {} });
export const useSpring = () => ({ get: () => 0, set: () => {} });

// Placeholder AnimatePresence
export const AnimatePresence = ({ children }: { children: React.ReactNode }) => <>{children}</>;
