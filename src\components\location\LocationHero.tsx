'use client'

import Image from 'next/image'
import Link from 'next/link'
import { useRouter } from 'next/navigation'

interface LocationHeroProps {
  location: string
  propertyType: string
  transactionType: string
  locationInfo: {
    name: string
    description: string
    featuredImage?: {
      url: string
      alt?: string
    }
    heroImage?: string // For backward compatibility
  }
}

const LocationHero = ({ location, propertyType, transactionType, locationInfo }: LocationHeroProps) => {
  const router = useRouter()

  // Format display text
  const propertyTypeFormatted = propertyType.charAt(0).toUpperCase() + propertyType.slice(1)
  const transactionFormatted = transactionType === 'long-term-rental' ? 'Long-term Rental' :
                              transactionType === 'for-sale-freehold' ? 'For Sale Freehold' :
                              'For Sale Leasehold'

  const isRental = transactionType.includes('rental')

  // Check if this is a high-priority SEO page
  const isHighPriority = (
    (location === 'canggu' && propertyType === 'villa' && (transactionType === 'long-term-rental' || transactionType === 'for-sale-freehold')) ||
    (location === 'ubud' && propertyType === 'villa' && (transactionType === 'long-term-rental' || transactionType === 'for-sale-freehold')) ||
    (location === 'seminyak' && propertyType === 'villa' && transactionType === 'long-term-rental')
  )

  // SEO-optimized headlines for high-priority pages with PRD keywords
  const getOptimizedHeadline = () => {
    if (location === 'canggu' && propertyType === 'villa' && transactionType === 'long-term-rental') {
      return 'Rent Villa in Canggu | Living in Canggu | Bali Digital Nomad Community'
    } else if (location === 'ubud' && propertyType === 'villa' && transactionType === 'long-term-rental') {
      return 'Rent Villa in Ubud | Bali Digital Nomad Community | Bali Coworking Spaces'
    } else if (location === 'seminyak' && propertyType === 'villa' && transactionType === 'long-term-rental') {
      return 'Rent Villa in Seminyak | Beach Villa Bali for Rent | Luxury Villa Bali'
    } else if (location === 'canggu' && propertyType === 'villa' && transactionType === 'for-sale-freehold') {
      return 'Buy Villa in Bali | Canggu Property Investment | Investment Property Bali'
    } else if (location === 'ubud' && propertyType === 'villa' && transactionType === 'for-sale-freehold') {
      return 'Buy Villa in Bali Ubud | Buying Land in Bali | Bali Investment Hotspots'
    }
    return `${locationInfo.name} ${propertyTypeFormatted} ${transactionFormatted}`
  }

  // SEO-optimized descriptions for high-priority pages
  const getOptimizedDescription = () => {
    if (location === 'canggu' && propertyType === 'villa' && transactionType === 'long-term-rental') {
      return 'Discover premium Canggu villa rentals from $1,500/month. Beachfront locations, modern amenities, rice field views. Perfect for expats, digital nomads, and surf enthusiasts seeking long-term accommodation in Bali\'s most vibrant beach town.'
    } else if (location === 'ubud' && propertyType === 'villa' && transactionType === 'long-term-rental') {
      return 'Find peaceful Ubud villa rentals from $1,200/month. Jungle retreats, rice terrace views, yoga spaces. Ideal for wellness-focused expats, remote workers, and those seeking spiritual growth in Bali\'s cultural heart.'
    } else if (location === 'seminyak' && propertyType === 'villa' && transactionType === 'long-term-rental') {
      return 'Luxury Seminyak villa rentals from $2,500/month. Beach proximity, sophisticated design, premium amenities. Perfect for executives, luxury lifestyle seekers, and those wanting upscale living near Bali\'s finest beaches and dining.'
    } else if (location === 'canggu' && propertyType === 'villa' && transactionType === 'for-sale-freehold') {
      return 'Premium Canggu villas for sale from $350K. Investment opportunities, beachfront properties, modern designs. Full ownership rights for foreigners through legal PT PMA structure. High rental yields and capital appreciation potential.'
    } else if (location === 'ubud' && propertyType === 'villa' && transactionType === 'for-sale-freehold') {
      return 'Exclusive Ubud villas for sale from $280K. Jungle settings, rice field views, wellness-focused designs. Secure investment with full ownership rights. Perfect for those seeking tranquil retreat properties with strong rental potential.'
    }
    return locationInfo.description || `Find your perfect ${propertyType} ${transactionType.replace('-', ' ')} in ${locationInfo.name}, Bali.`
  }

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0 z-0">
        <Image
          src={locationInfo.featuredImage?.url || locationInfo.heroImage || '/images/default-hero.svg'}
          alt={locationInfo.featuredImage?.alt || `${locationInfo.name} ${propertyType} - Beautiful ${transactionFormatted.toLowerCase()} properties`}
          fill
          className="object-cover"
          priority
          placeholder="blur"
          blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
        />
        {/* Overlay */}
        <div className="absolute inset-0 bg-black/50" />
      </div>

      {/* Content */}
      <div className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className="mb-6">
          {/* Breadcrumb */}
          <nav className="text-white/80 text-sm mb-4">
            <Link href="/" className="hover:text-white transition-colors">Home</Link>
            <span className="mx-2">→</span>
            <Link href={`/${location}`} className="hover:text-white transition-colors capitalize">{location}</Link>
            <span className="mx-2">→</span>
            <span className="text-white capitalize">{propertyType} {transactionFormatted}</span>
          </nav>

          {/* Priority Badge - Only show for high-priority pages */}
          {isHighPriority && (
            <div className="inline-flex items-center bg-emerald-500 text-white px-4 py-2 rounded-full text-sm font-semibold mb-6">
              <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
              Premium Location
            </div>
          )}
        </div>

        {/* Main Headline - SEO Optimized H1 */}
        <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
          {getOptimizedHeadline()}
        </h1>

        {/* Location-specific Subtitle */}
        <p className="text-xl sm:text-2xl text-gray-200 mb-8 max-w-4xl mx-auto leading-relaxed">
          {getOptimizedDescription()}
        </p>

        {/* Key Benefits */}
        <div className="flex flex-wrap justify-center items-center gap-6 mb-10 text-white/90">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <span className="text-sm sm:text-base font-medium">Local Expertise</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <span className="text-sm sm:text-base font-medium">
              {isRental ? 'Verified Properties' : 'Legal Compliance'}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <span className="text-sm sm:text-base font-medium">
              {isRental ? 'Move-in Ready' : 'Investment Support'}
            </span>
          </div>
        </div>

        {/* Primary CTAs - PRD Compliant Chatbot Links */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
          <button
            onClick={() => {
              const query = isRental ? `rent-${propertyType}-in-${location}` : `buy-${propertyType}-in-${location}`;
              window.location.href = `https://app.balipropertyscout.com?query=${query}`
            }}
            className="bg-emerald-600 hover:bg-emerald-700 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl w-full sm:w-auto"
          >
            {isHighPriority && isRental ? 'View Premium Rentals' :
             isHighPriority && !isRental ? 'View Investment Properties' :
             isRental ? 'View Available Properties' : 'View Properties for Sale'}
          </button>
          <button
            onClick={() => {
              router.push(`/chatbot?query=schedule-viewing`)
            }}
            className="bg-white/10 hover:bg-white/20 text-white border-2 border-white/30 hover:border-white/50 px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 backdrop-blur-sm w-full sm:w-auto"
          >
            {isHighPriority && isRental ? 'Schedule Private Viewing' :
             isHighPriority && !isRental ? 'Get Investment Analysis' :
             isRental ? 'Schedule Viewing' : 'Get Investment Guide'}
          </button>
        </div>

        {/* Secondary CTA */}
        <div className="mb-8">
          <Link
            href="#contact"
            className="text-white/80 hover:text-white underline underline-offset-4 text-sm sm:text-base transition-colors duration-300"
          >
            Get personalized {location} property recommendations →
          </Link>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
            <div className="text-2xl font-bold text-white">50+</div>
            <div className="text-white/80 text-sm">Available Properties</div>
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
            <div className="text-2xl font-bold text-white">5+</div>
            <div className="text-white/80 text-sm">Years Experience</div>
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
            <div className="text-2xl font-bold text-white">24/7</div>
            <div className="text-white/80 text-sm">Support</div>
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
            <div className="text-2xl font-bold text-white">100%</div>
            <div className="text-white/80 text-sm">Legal Compliance</div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10">
        <div className="animate-bounce">
          <svg
            className="w-6 h-6 text-white/70"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 14l-7 7m0 0l-7-7m7 7V3"
            />
          </svg>
        </div>
      </div>
    </section>
  )
}

export default LocationHero
