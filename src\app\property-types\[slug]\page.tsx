// STORY-013: Individual Property Type Pages - Enhanced with Modern Design
import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import Link from 'next/link'
import { getPropertyTypeBySlug, getPropertyTypes } from '@/lib/payload'
import { richTextToPlainText } from '@/lib/richtext-utils'
import { CleanModernHero as ModernHero } from '@/components/ui/CleanModernHero'

interface PropertyTypePageProps {
  params: Promise<{
    slug: string
  }>
}

export async function generateStaticParams() {
  const propertyTypes = await getPropertyTypes()
  return propertyTypes.map((propertyType: any) => ({
    slug: propertyType.slug,
  }))
}

export async function generateMetadata({ params }: PropertyTypePageProps): Promise<Metadata> {
  const { slug } = await params
  const propertyType = await getPropertyTypeBySlug(slug)
  
  if (!propertyType) {
    return {
      title: 'Property Type Not Found',
    }
  }

  const metaTitle = propertyType.seoContent?.metaTitle || 
    `${propertyType.name} Properties in Bali - Rental & Sale | Bali Real Estate`
  
  const metaDescription = propertyType.seoContent?.metaDescription ||
    richTextToPlainText(propertyType.description).substring(0, 160) ||
    `Discover ${propertyType.name.toLowerCase()} properties in Bali. Expert guidance for rentals, sales, and investment opportunities.`

  const keywords = propertyType.seoContent?.targetKeywords?.map((k: any) => k.keyword).join(', ') ||
    `${propertyType.name} Bali, ${propertyType.name} rental, ${propertyType.name} for sale, Bali property`

  return {
    title: metaTitle,
    description: metaDescription,
    keywords: keywords,
    openGraph: {
      title: metaTitle,
      description: metaDescription,
      type: 'website',
    },
  }
}

export default async function PropertyTypePage({ params }: PropertyTypePageProps) {
  const { slug } = await params
  const propertyType = await getPropertyTypeBySlug(slug)

  if (!propertyType) {
    notFound()
  }

  // Get background image based on property type
  const getBackgroundImage = (slug: string) => {
    switch (slug) {
      case 'villa':
        return 'https://images.unsplash.com/photo-1571896349842-33c89424de2d?q=80&w=2080&auto=format&fit=crop'
      case 'guesthouse':
        return 'https://images.unsplash.com/photo-1566073771259-6a8506099945?q=80&w=2070&auto=format&fit=crop'
      case 'apartment':
        return 'https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?q=80&w=2070&auto=format&fit=crop'
      default:
        return 'https://images.unsplash.com/photo-1537953773345-d172ccf13cf1?q=80&w=2070&auto=format&fit=crop'
    }
  }

  return (
    <main className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100">
      {/* Modern Hero Section */}
      <ModernHero
        title={`Premium ${propertyType.name} Properties in Bali`}
        subtitle={`${richTextToPlainText(propertyType.description)} Discover exceptional ${propertyType.name.toLowerCase()} options with expert local guidance and comprehensive support.`}
        backgroundImage={getBackgroundImage(propertyType.slug)}
        primaryCTA={{
          text: "Start Your Search",
          href: `/chatbot?query=search-${propertyType.slug}-in-bali`
        }}
        secondaryCTA={{
          text: "Browse Locations",
          href: "/locations"
        }}
      />

      {/* Premium Price Range Section */}
      <section className="py-24 relative">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-br from-emerald-50/50 via-transparent to-teal-50/50" />
        <div className="absolute inset-0 opacity-40" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }} />

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
              <span className="bg-gradient-to-r from-emerald-600 to-emerald-700 bg-clip-text text-transparent">
                Investment Overview
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Comprehensive pricing and investment insights for {propertyType.name.toLowerCase()} properties in Bali's premium locations.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {/* Rental Pricing Card */}
            {propertyType.priceRanges?.rental && (
              <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 border border-white/50">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mb-6">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Rental Options</h3>

                {propertyType.priceRanges.rental.shortTerm && (
                  <div className="flex justify-between items-center mb-4 pb-4 border-b border-gray-200">
                    <span className="text-gray-700 font-medium">Short-term Rental:</span>
                    <span className="font-bold text-blue-600 text-lg">{propertyType.priceRanges.rental.shortTerm}</span>
                  </div>
                )}

                {propertyType.priceRanges.rental.longTerm && (
                  <div className="flex justify-between items-center">
                    <span className="text-gray-700 font-medium">Long-term Rental:</span>
                    <span className="font-bold text-blue-600 text-lg">{propertyType.priceRanges.rental.longTerm}</span>
                  </div>
                )}
              </div>
            )}

            {/* Purchase Pricing Card */}
            {propertyType.priceRanges?.purchase && (
              <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 border border-white/50">
                <div className="w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center mb-6">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                  </svg>
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Purchase Options</h3>

                {propertyType.priceRanges.purchase.freehold && (
                  <div className="flex justify-between items-center mb-4 pb-4 border-b border-gray-200">
                    <span className="text-gray-700 font-medium">Freehold:</span>
                    <span className="font-bold text-emerald-600 text-lg">{propertyType.priceRanges.purchase.freehold}</span>
                  </div>
                )}

                {propertyType.priceRanges.purchase.leasehold && (
                  <div className="flex justify-between items-center">
                    <span className="text-gray-700 font-medium">Leasehold:</span>
                    <span className="font-bold text-emerald-600 text-lg">{propertyType.priceRanges.purchase.leasehold}</span>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Detailed Description */}
      {propertyType.detailedDescription && (
        <section className="py-24 relative">
          {/* Premium Background */}
          <div className="absolute inset-0 bg-gradient-to-br from-white via-gray-50 to-emerald-50/30" />
          <div className="absolute inset-0 opacity-50" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23000000' fill-opacity='0.03' fill-rule='evenodd'%3E%3Cpath d='m0 40l40-40h-40v40zm40 0v-40h-40l40 40z'/%3E%3C/g%3E%3C/svg%3E")`
          }} />

          <div className="relative max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
                <span className="bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                  About {propertyType.name} Properties
                </span>
              </h2>
              <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                Discover what makes {propertyType.name.toLowerCase()} properties in Bali exceptional investment and lifestyle choices.
              </p>
            </div>

            <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-12 shadow-xl border border-white/50">
              <div className="prose prose-xl max-w-none text-gray-700 leading-relaxed">
                <p className="text-lg">{richTextToPlainText(propertyType.detailedDescription)}</p>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Premium Features Section */}
      {propertyType.typicalFeatures && propertyType.typicalFeatures.length > 0 && (
        <section className="py-24 relative">
          {/* Background Pattern */}
          <div className="absolute inset-0 bg-gradient-to-br from-emerald-50/50 via-transparent to-teal-50/50" />
          <div className="absolute inset-0 opacity-40" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
          }} />

          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
                <span className="bg-gradient-to-r from-emerald-600 to-emerald-700 bg-clip-text text-transparent">
                  Premium Features
                </span>
                <br />
                <span className="text-gray-700">& Amenities</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Discover the exceptional features and amenities that make {propertyType.name.toLowerCase()} properties in Bali truly special.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {propertyType.typicalFeatures.map((feature: any, index: number) => (
                <div key={index} className="group bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 border border-white/50 hover:border-emerald-200/50">
                  <div className="flex items-start">
                    <div className="w-4 h-4 bg-gradient-to-r from-emerald-400 to-teal-400 rounded-full mt-2 mr-6 flex-shrink-0 group-hover:scale-110 transition-transform duration-300"></div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-emerald-600 transition-colors duration-300">
                        {feature.feature}
                      </h3>
                      {feature.description && (
                        <p className="text-gray-600 leading-relaxed">{feature.description}</p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Premium Target Audience Section */}
      {propertyType.targetAudience && propertyType.targetAudience.length > 0 && (
        <section className="py-24 relative">
          {/* Premium Background */}
          <div className="absolute inset-0 bg-gradient-to-br from-white via-gray-50 to-emerald-50/30" />
          <div className="absolute inset-0 opacity-50" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23000000' fill-opacity='0.03' fill-rule='evenodd'%3E%3Cpath d='m0 40l40-40h-40v40zm40 0v-40h-40l40 40z'/%3E%3C/g%3E%3C/svg%3E")`
          }} />

          <div className="relative max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div className="mb-16">
              <h2 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
                <span className="bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                  Perfect For
                </span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Discover who benefits most from {propertyType.name.toLowerCase()} properties in Bali's premium locations.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-4xl mx-auto">
              {propertyType.targetAudience.map((item: any, index: number) => (
                <div key={index} className="group bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-500 border border-white/50 hover:border-emerald-200/50">
                  <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <span className="text-lg font-semibold text-gray-900 capitalize group-hover:text-emerald-600 transition-colors duration-300">
                    {item.audience?.replace('-', ' ') || item}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Premium Investment Analysis */}
      {propertyType.investmentAnalysis && (
        <section className="py-24 relative">
          {/* Background Pattern */}
          <div className="absolute inset-0 bg-gradient-to-br from-emerald-50/50 via-transparent to-teal-50/50" />
          <div className="absolute inset-0 opacity-40" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
          }} />

          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
                <span className="bg-gradient-to-r from-emerald-600 to-emerald-700 bg-clip-text text-transparent">
                  Investment Potential
                </span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Comprehensive analysis of {propertyType.name.toLowerCase()} investment opportunities in Bali's growing market.
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8 max-w-6xl mx-auto">
              {/* Key Metrics Card */}
              <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 border border-white/50">
                <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mb-6">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Key Metrics</h3>

                {propertyType.investmentAnalysis.rentalYield && (
                  <div className="flex justify-between items-center mb-6 pb-6 border-b border-gray-200">
                    <span className="text-gray-700 font-medium">Expected Rental Yield:</span>
                    <span className="font-bold text-purple-600 text-xl">{propertyType.investmentAnalysis.rentalYield}</span>
                  </div>
                )}

                {propertyType.investmentAnalysis.appreciationRate && (
                  <div className="flex justify-between items-center">
                    <span className="text-gray-700 font-medium">Appreciation Rate:</span>
                    <span className="font-bold text-purple-600 text-xl">{propertyType.investmentAnalysis.appreciationRate}</span>
                  </div>
                )}
              </div>

              {/* Investment Highlights Card */}
              {propertyType.investmentAnalysis.investmentHighlights && (
                <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 border border-white/50">
                  <div className="w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center mb-6">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-6">Investment Highlights</h3>
                  <div className="space-y-4">
                    {propertyType.investmentAnalysis.investmentHighlights.map((highlight: any, index: number) => (
                      <div key={index} className="flex items-start">
                        <div className="w-3 h-3 bg-gradient-to-r from-emerald-400 to-teal-400 rounded-full mt-2 mr-4 flex-shrink-0"></div>
                        <span className="text-gray-700 leading-relaxed">{highlight.highlight}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </section>
      )}

      {/* Premium CTA Section */}
      <section className="py-24 relative overflow-hidden">
        {/* Premium Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-emerald-600 via-emerald-500 to-teal-600" />
        <div className="absolute inset-0 opacity-30" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`
        }} />

        {/* Floating Elements */}
        <div className="absolute top-20 left-10 w-2 h-2 bg-white/30 rounded-full animate-float" />
        <div className="absolute top-40 right-20 w-1 h-1 bg-white/40 rounded-full animate-float" style={{ animationDelay: '1s' }} />
        <div className="absolute bottom-32 left-1/4 w-1.5 h-1.5 bg-white/35 rounded-full animate-float" style={{ animationDelay: '2s' }} />

        <div className="relative max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl sm:text-5xl font-bold text-white mb-8">
              Ready to Explore
              <br />
              <span className="bg-gradient-to-r from-white via-gray-100 to-gray-300 bg-clip-text text-transparent">
                {propertyType.name} Options?
              </span>
            </h2>

            <p className="text-xl sm:text-2xl text-emerald-100 mb-12 leading-relaxed">
              Get personalized recommendations and expert guidance for your {propertyType.name.toLowerCase()} search in Bali.
              Our local experts are ready to help you find the perfect property.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              {/* Primary CTA */}
              <Link
                href={`/chatbot?query=search-${propertyType.slug}-in-bali`}
                className="group relative inline-flex items-center justify-center px-10 py-5 text-lg font-semibold text-emerald-600 bg-white rounded-2xl shadow-2xl transition-all duration-500 hover:shadow-white/40 hover:shadow-2xl overflow-hidden hover:scale-105"
              >
                {/* Animated Background */}
                <div className="absolute inset-0 bg-gradient-to-r from-gray-50 via-white to-gray-50 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                {/* Shimmer Effect */}
                <div className="absolute inset-0 -skew-x-12 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 group-hover:animate-shimmer" />

                <span className="relative z-10 flex items-center">
                  Start Your Search
                  <svg className="w-5 h-5 ml-3 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                </span>
              </Link>

              {/* Secondary CTA */}
              <Link
                href="/contact"
                className="group inline-flex items-center justify-center px-10 py-5 text-lg font-semibold text-white border border-white/20 rounded-2xl backdrop-blur-md bg-white/5 transition-all duration-500 hover:bg-white/10 hover:border-white/30 hover:backdrop-blur-lg"
              >
                <span className="flex items-center">
                  Get Expert Consultation
                  <svg className="w-5 h-5 ml-3 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                  </svg>
                </span>
              </Link>
            </div>

            {/* Trust Indicators */}
            <div className="flex flex-wrap justify-center items-center gap-8 mt-12 opacity-80">
              <div className="flex items-center text-emerald-100">
                <svg className="w-5 h-5 text-white mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-sm font-medium">Free Consultation</span>
              </div>
              <div className="flex items-center text-emerald-100">
                <svg className="w-5 h-5 text-white mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-sm font-medium">Local Expertise</span>
              </div>
              <div className="flex items-center text-emerald-100">
                <svg className="w-5 h-5 text-white mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-sm font-medium">Full Support</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
