import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import Link from 'next/link'
import { getLocationBySlug, getLocations } from '@/lib/payload'
import QuickContactForm from '@/components/forms/QuickContactForm'
import ComprehensiveLocationContent from '@/components/location/ComprehensiveLocationContent'
import { Breadcrumbs } from '@/components/common/Breadcrumbs'
import { InternalLinking } from '@/components/seo/InternalLinking'

// Generate static params for all locations
export async function generateStaticParams() {
  const locations = await getLocations()
  return locations.map((location: any) => ({
    slug: location.slug,
  }))
}

// Generate metadata for each location
export async function generateMetadata({ params }: { params: { slug: string } }): Promise<Metadata> {
  const location = await getLocationBySlug(params.slug)
  
  if (!location) {
    return {
      title: 'Location Not Found',
      description: 'The requested location could not be found.',
    }
  }

  return {
    title: `${location.name} - Bali Real Estate | Long-term Rentals & Property Sales`,
    description: location.description || `Discover ${location.name} - your perfect location for long-term rentals and property investment in Bali. Expert guidance and premium properties available.`,
    keywords: `${location.name}, Bali real estate, long-term rental, property investment, ${location.name} properties`,
    openGraph: {
      title: `${location.name} - Bali Real Estate`,
      description: location.description || `Discover ${location.name} for your Bali property investment`,
      type: 'website',
    },
  }
}

interface LocationPageProps {
  params: {
    slug: string
  }
}

export default async function LocationPage({ params }: LocationPageProps) {
  const location = await getLocationBySlug(params.slug)

  if (!location) {
    notFound()
  }

  return (
    <main className="min-h-screen bg-white">
      {/* Modern Breadcrumbs - PRD Requirement */}
      <div className="bg-gradient-to-r from-emerald-50 to-white py-6 border-b border-emerald-100">
        <div className="max-w-6xl mx-auto px-4">
          <Breadcrumbs />
        </div>
      </div>

      {/* Modern Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-slate-900 via-emerald-900 to-emerald-800">
        {/* Premium Background with Gradient Mesh */}
        <div className="absolute inset-0 bg-gradient-to-br from-black/60 via-black/40 to-black/60" />
        <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/10 via-transparent to-emerald-600/10 animate-pulse" />
        
        {/* Noise Texture for Premium Feel */}
        <div className="absolute inset-0 opacity-20" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`
        }} />

        {/* Premium Content */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold mb-8 leading-tight">
              <span className="bg-gradient-to-r from-white via-emerald-100 to-emerald-200 bg-clip-text text-transparent">
                {location.name}
              </span>
              <br />
              <span className="text-white text-4xl md:text-5xl lg:text-6xl font-semibold">
                Real Estate
              </span>
            </h1>

            <p className="text-xl md:text-2xl text-emerald-100 mb-12 max-w-4xl mx-auto leading-relaxed">
              {location.description}
            </p>

            {/* Modern Key Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12 max-w-4xl mx-auto">
              {location.priceRanges?.villa?.longTermRental && (
                <div className="group bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-white/20 hover:bg-white/20 transition-all duration-500 transform hover:scale-105">
                  <div className="text-3xl md:text-4xl font-bold text-white mb-2">
                    {location.priceRanges.villa.longTermRental}
                  </div>
                  <div className="text-emerald-200 font-medium">Villa Rentals</div>
                </div>
              )}
              
              {location.priceRanges?.apartment?.longTermRental && (
                <div className="group bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-white/20 hover:bg-white/20 transition-all duration-500 transform hover:scale-105">
                  <div className="text-3xl md:text-4xl font-bold text-white mb-2">
                    {location.priceRanges.apartment.longTermRental}
                  </div>
                  <div className="text-emerald-200 font-medium">Apartments</div>
                </div>
              )}
              
              <div className="group bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-white/20 hover:bg-white/20 transition-all duration-500 transform hover:scale-105">
                <div className="text-3xl md:text-4xl font-bold text-white mb-2">
                  {location.priority === 'zeer-hoog' ? 'Premium' : 
                   location.priority === 'hoog' ? 'Popular' : 'Growing'}
                </div>
                <div className="text-emerald-200 font-medium">Market Status</div>
              </div>
            </div>

            {/* Premium CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <Link
                href={`/chatbot?query=rent-villa-in-${location.slug}`}
                className="group relative inline-flex items-center justify-center px-10 py-5 text-lg font-semibold text-emerald-900 bg-white rounded-2xl shadow-2xl transition-all duration-500 hover:shadow-white/20 hover:shadow-2xl overflow-hidden transform hover:scale-105"
              >
                <div className="absolute inset-0 bg-emerald-50 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <span className="relative z-10 flex items-center">
                  Find Properties
                  <svg className="w-5 h-5 ml-2 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                  </svg>
                </span>
              </Link>
              
              <Link
                href="#contact"
                className="group relative inline-flex items-center justify-center px-10 py-5 text-lg font-semibold text-white bg-emerald-600/20 backdrop-blur-md rounded-2xl border border-emerald-400/30 transition-all duration-500 hover:bg-emerald-600/30 transform hover:scale-105"
              >
                <span className="relative z-10 flex items-center">
                  Get Expert Advice
                  <svg className="w-5 h-5 ml-2 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.955 8.955 0 01-2.697-.413l-3.178 1.589a.75.75 0 01-1.072-.71l.004-2.47A8 8 0 013 12a8 8 0 018-8 8 8 0 018 8z" />
                  </svg>
                </span>
              </Link>
            </div>
          </div>
        </div>

        {/* Floating Elements for Premium Feel */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-emerald-400/30 rounded-full animate-pulse" />
          <div className="absolute top-1/3 right-1/3 w-1 h-1 bg-emerald-400/40 rounded-full animate-pulse" style={{ animationDelay: '1s' }} />
        </div>
      </section>

      {/* Modern Sticky Navigation */}
      <nav className="sticky top-0 z-50 bg-white/80 backdrop-blur-md border-b border-emerald-100 shadow-lg">
        <div className="max-w-6xl mx-auto px-4">
          <div className="flex items-center justify-center py-4">
            <div className="flex space-x-8">
              {[
                { href: '#overview', label: 'Overview' },
                { href: '#amenities', label: 'Amenities' },
                { href: '#properties', label: 'Properties' },
                { href: '#lifestyle', label: 'Lifestyle' },
                { href: '#faq', label: 'FAQ' },
                { href: '#contact', label: 'Contact' }
              ].map((item) => (
                <a
                  key={item.href}
                  href={item.href}
                  className="group relative px-4 py-2 text-gray-700 hover:text-emerald-600 font-medium transition-all duration-300 rounded-full hover:bg-emerald-50"
                >
                  <span className="relative z-10">{item.label}</span>
                  <div className="absolute inset-0 bg-emerald-100 rounded-full scale-0 group-hover:scale-100 transition-transform duration-300 opacity-0 group-hover:opacity-100" />
                </a>
              ))}
            </div>
          </div>
        </div>
      </nav>

      {/* Rest of the content will be added in the next part */}
    </main>
  )
}
