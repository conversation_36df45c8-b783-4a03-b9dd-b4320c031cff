/**
 * Header Component Tests
 * 
 * Unit tests for the Header navigation component
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Header } from '@/components/layout/Header';

// Mock next/link
jest.mock('next/link', () => {
  return ({ children, href, ...props }: any) => (
    <a href={href} {...props}>
      {children}
    </a>
  );
});

describe('Header Component', () => {
  beforeEach(() => {
    // Reset any mocks before each test
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders the header with logo', () => {
      render(<Header />);
      
      const logo = screen.getByRole('link', { name: /bali real estate/i });
      expect(logo).toBeInTheDocument();
      expect(logo).toHaveAttribute('href', '/');
    });

    it('renders main navigation links', () => {
      render(<Header />);
      
      expect(screen.getByRole('link', { name: /locations/i })).toBeInTheDocument();
      expect(screen.getByRole('link', { name: /property types/i })).toBeInTheDocument();
      expect(screen.getByRole('link', { name: /contact/i })).toBeInTheDocument();
    });

    it('renders mobile menu toggle button', () => {
      render(<Header />);
      
      const menuButton = screen.getByRole('button', { name: /toggle menu/i });
      expect(menuButton).toBeInTheDocument();
    });
  });

  describe('Navigation Functionality', () => {
    it('shows dropdown menu on hover for locations', async () => {
      const user = userEvent.setup();
      render(<Header />);
      
      const locationsLink = screen.getByRole('link', { name: /locations/i });
      
      await user.hover(locationsLink);
      
      await waitFor(() => {
        expect(screen.getByText(/canggu/i)).toBeInTheDocument();
        expect(screen.getByText(/seminyak/i)).toBeInTheDocument();
        expect(screen.getByText(/ubud/i)).toBeInTheDocument();
      });
    });

    it('shows dropdown menu on hover for property types', async () => {
      const user = userEvent.setup();
      render(<Header />);
      
      const propertyTypesLink = screen.getByRole('link', { name: /property types/i });
      
      await user.hover(propertyTypesLink);
      
      await waitFor(() => {
        expect(screen.getByText(/villa/i)).toBeInTheDocument();
        expect(screen.getByText(/apartment/i)).toBeInTheDocument();
      });
    });

    it('hides dropdown menu on mouse leave', async () => {
      const user = userEvent.setup();
      render(<Header />);
      
      const locationsLink = screen.getByRole('link', { name: /locations/i });
      
      // Show dropdown
      await user.hover(locationsLink);
      await waitFor(() => {
        expect(screen.getByText(/canggu/i)).toBeInTheDocument();
      });
      
      // Hide dropdown
      await user.unhover(locationsLink);
      await waitFor(() => {
        expect(screen.queryByText(/canggu/i)).not.toBeInTheDocument();
      });
    });
  });

  describe('Mobile Menu', () => {
    it('toggles mobile menu on button click', async () => {
      const user = userEvent.setup();
      render(<Header />);
      
      const menuButton = screen.getByRole('button', { name: /toggle menu/i });
      
      // Menu should be closed initially
      expect(screen.queryByTestId('mobile-menu')).not.toBeInTheDocument();
      
      // Open menu
      await user.click(menuButton);
      expect(screen.getByTestId('mobile-menu')).toBeInTheDocument();
      
      // Close menu
      await user.click(menuButton);
      expect(screen.queryByTestId('mobile-menu')).not.toBeInTheDocument();
    });

    it('closes mobile menu when clicking outside', async () => {
      const user = userEvent.setup();
      render(<Header />);
      
      const menuButton = screen.getByRole('button', { name: /toggle menu/i });
      
      // Open menu
      await user.click(menuButton);
      expect(screen.getByTestId('mobile-menu')).toBeInTheDocument();
      
      // Click outside
      await user.click(document.body);
      await waitFor(() => {
        expect(screen.queryByTestId('mobile-menu')).not.toBeInTheDocument();
      });
    });

    it('closes mobile menu on escape key', async () => {
      const user = userEvent.setup();
      render(<Header />);
      
      const menuButton = screen.getByRole('button', { name: /toggle menu/i });
      
      // Open menu
      await user.click(menuButton);
      expect(screen.getByTestId('mobile-menu')).toBeInTheDocument();
      
      // Press escape
      await user.keyboard('{Escape}');
      await waitFor(() => {
        expect(screen.queryByTestId('mobile-menu')).not.toBeInTheDocument();
      });
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA attributes for dropdown menus', () => {
      render(<Header />);
      
      const locationsButton = screen.getByRole('button', { name: /locations/i });
      expect(locationsButton).toHaveAttribute('aria-expanded', 'false');
      expect(locationsButton).toHaveAttribute('aria-haspopup', 'true');
    });

    it('updates ARIA attributes when dropdown is open', async () => {
      const user = userEvent.setup();
      render(<Header />);
      
      const locationsButton = screen.getByRole('button', { name: /locations/i });
      
      await user.hover(locationsButton);
      
      await waitFor(() => {
        expect(locationsButton).toHaveAttribute('aria-expanded', 'true');
      });
    });

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      render(<Header />);
      
      // Tab to first navigation item
      await user.tab();
      expect(screen.getByRole('link', { name: /locations/i })).toHaveFocus();
      
      // Tab to next item
      await user.tab();
      expect(screen.getByRole('link', { name: /property types/i })).toHaveFocus();
    });

    it('has proper heading structure', () => {
      render(<Header />);
      
      // Logo should be in an h1 or have proper heading role
      const logo = screen.getByRole('link', { name: /bali real estate/i });
      expect(logo.closest('h1')).toBeInTheDocument();
    });
  });

  describe('Responsive Design', () => {
    it('shows desktop navigation on large screens', () => {
      // Mock large screen
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 1024,
      });
      
      render(<Header />);
      
      const desktopNav = screen.getByTestId('desktop-navigation');
      expect(desktopNav).toBeInTheDocument();
      expect(desktopNav).not.toHaveClass('hidden');
    });

    it('hides desktop navigation on small screens', () => {
      // Mock small screen
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 640,
      });
      
      render(<Header />);
      
      const desktopNav = screen.getByTestId('desktop-navigation');
      expect(desktopNav).toHaveClass('hidden', 'md:flex');
    });
  });

  describe('Performance', () => {
    it('does not cause unnecessary re-renders', () => {
      const { rerender } = render(<Header />);
      
      // Re-render with same props
      rerender(<Header />);
      
      // Component should not re-render unnecessarily
      expect(screen.getByRole('navigation')).toBeInTheDocument();
    });

    it('lazy loads dropdown content', async () => {
      const user = userEvent.setup();
      render(<Header />);
      
      // Dropdown content should not be in DOM initially
      expect(screen.queryByText(/canggu/i)).not.toBeInTheDocument();
      
      // Content should load on hover
      const locationsLink = screen.getByRole('link', { name: /locations/i });
      await user.hover(locationsLink);
      
      await waitFor(() => {
        expect(screen.getByText(/canggu/i)).toBeInTheDocument();
      });
    });
  });
});
