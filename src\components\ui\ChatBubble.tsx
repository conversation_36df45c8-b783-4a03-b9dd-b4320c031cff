/**
 * Modern Chat Bubble Component
 * STORY-008: UI/UX Transformation
 */

'use client';

import { useState, useEffect } from 'react';
// import { motion, AnimatePresence } from 'motion/react'; // Temporarily disabled for debugging
import { useDarkMode } from './DarkModeProvider';

interface ChatBubbleProps {
  className?: string;
}

export function ChatBubble({ className = '' }: ChatBubbleProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [hasInteracted, setHasInteracted] = useState(false);
  const [mounted, setMounted] = useState(false);
  const { isDark } = useDarkMode();

  // Prevent hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  // Show bubble after a delay
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 3000); // Show after 3 seconds

    return () => clearTimeout(timer);
  }, []);

  // Pulse animation for attention
  useEffect(() => {
    if (!hasInteracted && isVisible) {
      const pulseTimer = setInterval(() => {
        // Trigger pulse animation via CSS class
      }, 5000);

      return () => clearInterval(pulseTimer);
    }
  }, [hasInteracted, isVisible]);

  const handleChatClick = () => {
    setHasInteracted(true);
    // Redirect to chatbot with general consultation query
    window.open('https://app.balipropertyscout.com?query=general-consultation', '_blank');
  };

  const handleToggle = () => {
    setIsOpen(!isOpen);
    setHasInteracted(true);
  };

  if (!mounted || !isVisible) return null;

  return (
    <div className={`fixed bottom-6 right-6 z-50 ${className}`}>
      {isOpen && (
        <div
          className={`
            mb-4 p-6 rounded-2xl shadow-2xl max-w-sm transition-all duration-300
            ${isDark ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'}
          `}
        >
            {/* Chat Preview Content */}
            <div className="space-y-4">
              {/* Header */}
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-full flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
                  </svg>
                </div>
                <div>
                  <h3 className={`font-semibold ${isDark ? 'text-white' : 'text-gray-900'}`}>
                    Bali Property Assistant
                  </h3>
                  <p className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                    Online now
                  </p>
                </div>
              </div>

              {/* Message */}
              <div className={`p-3 rounded-lg ${isDark ? 'bg-gray-700' : 'bg-gray-50'}`}>
                <p className={`text-sm ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
                  👋 Hi! I'm here to help you find the perfect property in Bali. What are you looking for?
                </p>
              </div>

              {/* Quick Actions */}
              <div className="space-y-2">
                <button
                  onClick={() => window.open('https://app.balipropertyscout.com?query=rent-villa-in-canggu', '_blank')}
                  className={`
                    w-full text-left p-2 rounded-lg text-sm transition-colors
                    ${isDark ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-100 text-gray-600'}
                  `}
                >
                  🏖️ Villa rentals in Canggu
                </button>
                <button
                  onClick={() => window.open('https://app.balipropertyscout.com?query=buy-apartment-in-seminyak', '_blank')}
                  className={`
                    w-full text-left p-2 rounded-lg text-sm transition-colors
                    ${isDark ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-100 text-gray-600'}
                  `}
                >
                  🏢 Apartments for sale in Seminyak
                </button>
                <button
                  onClick={() => window.open('https://app.balipropertyscout.com?query=investment-consultation', '_blank')}
                  className={`
                    w-full text-left p-2 rounded-lg text-sm transition-colors
                    ${isDark ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-100 text-gray-600'}
                  `}
                >
                  💰 Investment opportunities
                </button>
              </div>

              {/* Start Chat Button */}
              <button
                onClick={handleChatClick}
                className="w-full bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white py-3 px-4 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105"
              >
                Start Conversation
              </button>
            </div>
          </div>
        )}

      {/* Chat Bubble Button */}
      <button
        onClick={handleToggle}
        className={`
          relative w-16 h-16 rounded-full shadow-2xl transition-all duration-300 transform hover:scale-110 focus:outline-none focus:ring-4 focus:ring-emerald-500/30 border-2 border-white
          ${isOpen ? 'bg-gray-600 hover:bg-gray-700' : 'bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700'}
          ${!hasInteracted ? 'animate-bounce' : ''}
        `}
        aria-label={isOpen ? 'Close chat' : 'Open chat'}
      >
        {/* Notification Dot */}
        {!hasInteracted && (
          <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full border-2 border-white animate-pulse" />
        )}

        {/* Icon */}
        <div className="flex items-center justify-center w-full h-full">
          {isOpen ? (
            <svg
              className="w-6 h-6 text-white transition-all duration-200"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          ) : (
            <svg
              className="w-7 h-7 text-white transition-all duration-200"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
            </svg>
          )}
        </div>

        {/* Ripple Effect */}
        <div className="absolute inset-0 rounded-full bg-white/20 scale-0 group-hover:scale-100 transition-transform duration-300" />
      </button>

      {/* Floating Message */}
      {!hasInteracted && isVisible && (
        <div
          className={`
            absolute right-20 top-4 px-4 py-2 rounded-lg shadow-lg whitespace-nowrap transition-all duration-500
            ${isDark ? 'bg-gray-800 text-white border border-gray-700' : 'bg-white text-gray-900 border border-gray-200'}
          `}
        >
          <div className="text-sm font-medium">Need help finding a property?</div>
          <div className={`absolute right-0 top-1/2 transform translate-x-1 -translate-y-1/2 w-2 h-2 rotate-45 ${isDark ? 'bg-gray-800 border-r border-b border-gray-700' : 'bg-white border-r border-b border-gray-200'}`} />
        </div>
      )}
    </div>
  );
}

export default ChatBubble;
