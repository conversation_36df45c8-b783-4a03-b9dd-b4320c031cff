import { MetadataRoute } from 'next'
import { getLocations } from '@/lib/payload'

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://bali-real-estate.com'
  
  // Get all locations from CMS
  const locations = await getLocations()
  
  // Static pages - STORY-003 Enhanced
  const staticPages = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 1,
    },
    {
      url: `${baseUrl}/locations`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.9,
    },
    {
      url: `${baseUrl}/property-types`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.9,
    },
    {
      url: `${baseUrl}/services`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/contact`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/about`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.7,
    },
    {
      url: `${baseUrl}/chatbot`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.6,
    },
  ]

  // Property type pages - STORY-003 New Pages
  const propertyPages = [
    {
      url: `${baseUrl}/property-types/villa`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/property-types/guesthouse`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.7,
    },
    {
      url: `${baseUrl}/property-types/apartment`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.6,
    },
    {
      url: `${baseUrl}/property-types/land`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.6,
    },
  ]

  // Dynamic location pages
  const locationPages = locations.map((location: any) => ({
    url: `${baseUrl}/locations/${location.slug}`,
    lastModified: new Date(location.updatedAt || new Date()),
    changeFrequency: 'weekly' as const,
    priority: 0.8,
  }))

  // High Priority SEO Landing Pages (Phase 1)
  const priorityPages = [
    // ZEER HOOG Priority
    {
      url: `${baseUrl}/canggu/villa/long-term-rental`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.95,
    },
    {
      url: `${baseUrl}/ubud/villa/long-term-rental`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.95,
    },
    {
      url: `${baseUrl}/canggu/villa/for-sale-freehold`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.95,
    },
    {
      url: `${baseUrl}/ubud/villa/for-sale-freehold`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.95,
    },
    // HOOG Priority
    {
      url: `${baseUrl}/seminyak/villa/long-term-rental`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.9,
    },
  ]

  // Additional property type pages for all locations
  const propertyTypePages = locations.flatMap((location: any) => [
    {
      url: `${baseUrl}/${location.slug}/villa/for-sale-leasehold`,
      lastModified: new Date(location.updatedAt || new Date()),
      changeFrequency: 'weekly' as const,
      priority: 0.7,
    },
    {
      url: `${baseUrl}/${location.slug}/guesthouse/long-term-rental`,
      lastModified: new Date(location.updatedAt || new Date()),
      changeFrequency: 'weekly' as const,
      priority: 0.6,
    },
  ])

  return [...staticPages, ...propertyPages, ...locationPages, ...priorityPages, ...propertyTypePages]
}
