# PowerShell script to completely remove all motion elements and props
$filePath = "src\app\locations\[slug]\page.tsx"

# Read the file content
$content = Get-Content $filePath -Raw

# Replace all motion elements with regular HTML elements
$content = $content -replace '<motion\.([a-zA-Z]+)([^>]*?)>', '<$1>'
$content = $content -replace '</motion\.([a-zA-Z]+)>', '</$1>'

# Remove all motion-specific props (multiline support)
$content = $content -replace '\s*initial=\{[^}]*\}', ''
$content = $content -replace '\s*animate=\{[^}]*\}', ''
$content = $content -replace '\s*whileInView=\{[^}]*\}', ''
$content = $content -replace '\s*transition=\{[^}]*\}', ''
$content = $content -replace '\s*viewport=\{[^}]*\}', ''
$content = $content -replace '\s*style=\{[^}]*\}', ''

# Remove any remaining motion references
$content = $content -replace 'motion\.', ''

# Clean up any double spaces or empty lines
$content = $content -replace '\s+', ' '
$content = $content -replace '\n\s*\n', "`n"

# Write the content back to the file
Set-Content $filePath $content

Write-Host "All motion elements and props removed successfully!"
