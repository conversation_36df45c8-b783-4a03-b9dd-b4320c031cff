/**
 * Header Component
 *
 * Main navigation header with logo, navigation menu, and mobile responsive design.
 * Includes sticky behavior and smooth transitions.
 */

'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils'
import { DarkModeToggle } from '@/components/ui/DarkModeProvider';
import { Button } from '@/components/ui';

export interface HeaderProps {
  /** Custom className */
  className?: string;
  /** Whether header should be sticky */
  sticky?: boolean;
  /** Whether to show background on scroll */
  showBackgroundOnScroll?: boolean;
}

// Navigation items
const navigationItems = [
  {
    label: 'Home',
    href: '/',
  },
  {
    label: 'Locations',
    href: '/locations',
    children: [
      { label: 'All Locations', href: '/locations' },
      { label: 'Canggu', href: '/locations/canggu' },
      { label: 'Ubud', href: '/locations/ubud' },
      { label: 'Seminyak', href: '/locations/seminyak' },
      { label: 'Sanur', href: '/locations/sanur' },
      { label: 'Jimbaran', href: '/locations/jimbaran' },
    ],
  },
  {
    label: 'Property Types',
    href: '/property-types',
    children: [
      { label: 'All Property Types', href: '/property-types' },
      { label: 'Villas', href: '/property-types/villa' },
      { label: 'Guesthouses', href: '/property-types/guesthouse' },
      // Apartments and Land hidden until implemented (PRD requirement)
      // { label: 'Apartments', href: '/property-types/apartment' },
      // { label: 'Land', href: '/property-types/land' },
    ],
  },
  {
    label: 'Services',
    href: '/services',
  },
  {
    label: 'About',
    href: '/about',
  },
  {
    label: 'Contact',
    href: '/contact',
  },
];

export const Header: React.FC<HeaderProps> = ({
  className,
  sticky = true,
  showBackgroundOnScroll = true,
}) => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const pathname = usePathname();

  // Handle scroll effect
  useEffect(() => {
    if (!showBackgroundOnScroll) return;

    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [showBackgroundOnScroll]);

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMobileMenuOpen(false);
    setActiveDropdown(null);
  }, [pathname]);

  // Check if link is active
  const isActiveLink = (href: string) => {
    if (href === '/') {
      return pathname === '/';
    }
    return pathname.startsWith(href);
  };

  // Handle dropdown toggle
  const handleDropdownToggle = (label: string) => {
    setActiveDropdown(activeDropdown === label ? null : label);
  };

  return (
    <header
      className={cn(
        'fixed top-4 left-4 right-4 z-50 transition-all duration-500 ease-out',
        isScrolled ? 'top-2 scale-95' : 'top-4 scale-100',
        className
      )}
    >
      <div className="bg-white/90 backdrop-blur-xl shadow-2xl rounded-2xl border border-white/20 max-w-7xl mx-auto px-6 sm:px-8 lg:px-10">
        <div className="flex items-center justify-between h-16 lg:h-18">
          {/* Logo */}
          <Link
            href="/"
            className="flex items-center space-x-2 text-primary font-bold text-xl lg:text-2xl"
          >
            <div className="w-8 h-8 lg:w-10 lg:h-10 bg-primary rounded-lg flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-sm lg:text-base">
                BR
              </span>
            </div>
            <span className="hidden sm:block">Bali Real Estate</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8">
            {navigationItems.map((item) => (
              <div key={item.label} className="relative group">
                {item.children ? (
                  // Dropdown menu
                  <div>
                    <button
                      className={cn(
                        'flex items-center space-x-1 px-3 py-2 text-sm font-medium transition-colors',
                        isActiveLink(item.href)
                          ? 'text-primary'
                          : 'text-neutral-700 hover:text-primary'
                      )}
                      onMouseEnter={() => setActiveDropdown(item.label)}
                      onMouseLeave={() => setActiveDropdown(null)}
                    >
                      <span>{item.label}</span>
                      <svg
                        className="w-4 h-4 transition-transform group-hover:rotate-180"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 9l-7 7-7-7"
                        />
                      </svg>
                    </button>

                    {/* Dropdown content */}
                    <div
                      className={cn(
                        'absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-neutral-200 py-2 transition-all duration-200',
                        activeDropdown === item.label
                          ? 'opacity-100 visible translate-y-0'
                          : 'opacity-0 invisible -translate-y-2'
                      )}
                      onMouseEnter={() => setActiveDropdown(item.label)}
                      onMouseLeave={() => setActiveDropdown(null)}
                      role="menu"
                      aria-label={`${item.label} submenu`}
                      tabIndex={-1}
                    >
                      {item.children.map((child) => (
                        <Link
                          key={child.href}
                          href={child.href}
                          className={cn(
                            'block px-4 py-2 text-sm transition-colors',
                            isActiveLink(child.href)
                              ? 'text-primary bg-primary-lightest'
                              : 'text-neutral-700 hover:text-primary hover:bg-neutral-50'
                          )}
                        >
                          {child.label}
                        </Link>
                      ))}
                    </div>
                  </div>
                ) : (
                  // Regular link
                  <Link
                    href={item.href}
                    className={cn(
                      'px-3 py-2 text-sm font-medium transition-colors',
                      isActiveLink(item.href)
                        ? 'text-primary'
                        : 'text-neutral-700 hover:text-primary'
                    )}
                  >
                    {item.label}
                  </Link>
                )}
              </div>
            ))}
          </nav>

          {/* CTA Button & Dark Mode Toggle */}
          <div className="hidden lg:flex items-center space-x-4">
            <DarkModeToggle />
            <Button variant="outline" size="sm" asChild>
              <Link href="https://app.balipropertyscout.com?query=general-consultation">Find My Property</Link>
            </Button>
          </div>

          {/* Mobile menu button */}
          <button
            className="lg:hidden p-2 text-neutral-700 hover:text-primary transition-colors"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            aria-label="Toggle mobile menu"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              {isMobileMenuOpen ? (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              ) : (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              )}
            </svg>
          </button>
        </div>

        {/* Mobile Navigation */}
        <div
          className={cn(
            'lg:hidden overflow-hidden transition-all duration-300',
            isMobileMenuOpen ? 'max-h-screen pb-6' : 'max-h-0'
          )}
        >
          <nav className="pt-4 space-y-2">
            {navigationItems.map((item) => (
              <div key={item.label}>
                {item.children ? (
                  // Mobile dropdown
                  <div>
                    <button
                      className={cn(
                        'flex items-center justify-between w-full px-3 py-2 text-left text-base font-medium transition-colors',
                        isActiveLink(item.href)
                          ? 'text-primary'
                          : 'text-neutral-700 hover:text-primary'
                      )}
                      onClick={() => handleDropdownToggle(item.label)}
                    >
                      <span>{item.label}</span>
                      <svg
                        className={cn(
                          'w-4 h-4 transition-transform',
                          activeDropdown === item.label && 'rotate-180'
                        )}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 9l-7 7-7-7"
                        />
                      </svg>
                    </button>

                    {/* Mobile dropdown content */}
                    <div
                      className={cn(
                        'overflow-hidden transition-all duration-200',
                        activeDropdown === item.label ? 'max-h-48' : 'max-h-0'
                      )}
                    >
                      <div className="pl-6 space-y-1">
                        {item.children.map((child) => (
                          <Link
                            key={child.href}
                            href={child.href}
                            className={cn(
                              'block px-3 py-2 text-sm transition-colors',
                              isActiveLink(child.href)
                                ? 'text-primary'
                                : 'text-neutral-600 hover:text-primary'
                            )}
                          >
                            {child.label}
                          </Link>
                        ))}
                      </div>
                    </div>
                  </div>
                ) : (
                  // Mobile regular link
                  <Link
                    href={item.href}
                    className={cn(
                      'block px-3 py-2 text-base font-medium transition-colors',
                      isActiveLink(item.href)
                        ? 'text-primary'
                        : 'text-neutral-700 hover:text-primary'
                    )}
                  >
                    {item.label}
                  </Link>
                )}
              </div>
            ))}

            {/* Mobile CTA */}
            <div className="pt-4 px-3">
              <Button variant="primary" size="sm" fullWidth asChild>
                <Link href="https://app.balipropertyscout.com?query=general-consultation">Find My Property</Link>
              </Button>
            </div>
          </nav>
        </div>
      </div>
    </header>
  );
};
