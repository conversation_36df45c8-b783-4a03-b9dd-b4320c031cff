/**
 * Advanced Schema Markup Component
 * STORY-003: Advanced SEO & Content Optimization
 */

import { Organization, WebSite, BreadcrumbList, FAQPage, LocalBusiness, RealEstateAgent } from 'schema-dts';

interface AdvancedSchemaProps {
  type: 'organization' | 'website' | 'breadcrumbs' | 'faq' | 'local-business' | 'real-estate-agent' | 'property-listing';
  data?: any;
}

export function AdvancedSchema({ type, data }: AdvancedSchemaProps) {
  let schema: any = {};

  switch (type) {
    case 'organization':
      schema = generateOrganizationSchema();
      break;
    case 'website':
      schema = generateWebsiteSchema();
      break;
    case 'breadcrumbs':
      schema = generateBreadcrumbSchema(data);
      break;
    case 'faq':
      schema = generateFAQSchema(data);
      break;
    case 'local-business':
      schema = generateLocalBusinessSchema();
      break;
    case 'real-estate-agent':
      schema = generateRealEstateAgentSchema();
      break;
    case 'property-listing':
      schema = generatePropertyListingSchema(data);
      break;
    default:
      return null;
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
    />
  );
}

function generateOrganizationSchema(): Organization {
  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'Bali Real Estate',
    description: 'Expert real estate services in Bali since 2019. Helping expats, digital nomads and investors find their perfect home or investment property.',
    url: 'https://bali-real-estate.com',
    logo: 'https://bali-real-estate.com/images/logo.png',
    image: 'https://bali-real-estate.com/images/hero-bali.jpg',
    foundingDate: '2019',
    numberOfEmployees: '10-50',
    address: {
      '@type': 'PostalAddress',
      streetAddress: 'Jl. Pantai Berawa No. 123',
      addressLocality: 'Canggu',
      addressRegion: 'Badung',
      postalCode: '80361',
      addressCountry: 'ID'
    },
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '+*************',
      contactType: 'customer service',
      availableLanguage: ['English', 'Indonesian'],
      hoursAvailable: {
        '@type': 'OpeningHoursSpecification',
        dayOfWeek: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
        opens: '09:00',
        closes: '18:00'
      }
    },
    sameAs: [
      'https://facebook.com/balirealestate',
      'https://instagram.com/balirealestate',
      'https://linkedin.com/company/balirealestate'
    ],
    areaServed: {
      '@type': 'Place',
      name: 'Bali, Indonesia'
    },
    serviceArea: {
      '@type': 'GeoCircle',
      geoMidpoint: {
        '@type': 'GeoCoordinates',
        latitude: -8.3405,
        longitude: 115.0920
      },
      geoRadius: '50000'
    }
  };
}

function generateWebsiteSchema(): WebSite {
  return {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: 'Bali Real Estate',
    description: 'Find your perfect home or investment in Bali. Expert guidance for long-term rentals and property sales.',
    url: 'https://bali-real-estate.com',
    potentialAction: {
      '@type': 'SearchAction',
      target: 'https://app.balipropertyscout.com?query={search_term_string}',
      'query-input': 'required name=search_term_string'
    },
    publisher: {
      '@type': 'Organization',
      name: 'Bali Real Estate',
      logo: 'https://bali-real-estate.com/images/logo.png'
    }
  };
}

function generateBreadcrumbSchema(breadcrumbs: Array<{ name: string; url: string }>): BreadcrumbList {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((crumb, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: crumb.name,
      item: crumb.url
    }))
  };
}

function generateFAQSchema(faqs: Array<{ question: string; answer: string }>): FAQPage {
  return {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer
      }
    }))
  };
}

function generateLocalBusinessSchema(): LocalBusiness {
  return {
    '@context': 'https://schema.org',
    '@type': 'LocalBusiness',
    '@id': 'https://bali-real-estate.com/#localbusiness',
    name: 'Bali Real Estate',
    description: 'Professional real estate services in Bali. Specializing in long-term rentals, property sales, and investment consultation.',
    url: 'https://bali-real-estate.com',
    telephone: '+*************',
    email: '<EMAIL>',
    address: {
      '@type': 'PostalAddress',
      streetAddress: 'Jl. Pantai Berawa No. 123',
      addressLocality: 'Canggu',
      addressRegion: 'Badung',
      postalCode: '80361',
      addressCountry: 'ID'
    },
    geo: {
      '@type': 'GeoCoordinates',
      latitude: -8.6481,
      longitude: 115.1333
    },
    openingHoursSpecification: [
      {
        '@type': 'OpeningHoursSpecification',
        dayOfWeek: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
        opens: '09:00',
        closes: '18:00'
      },
      {
        '@type': 'OpeningHoursSpecification',
        dayOfWeek: 'Saturday',
        opens: '09:00',
        closes: '14:00'
      }
    ],
    priceRange: '$500-$5000',
    currenciesAccepted: 'USD, IDR',
    paymentAccepted: 'Cash, Credit Card, Bank Transfer',
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: '4.8',
      reviewCount: '127',
      bestRating: '5',
      worstRating: '1'
    }
  };
}

function generateRealEstateAgentSchema(): RealEstateAgent {
  return {
    '@context': 'https://schema.org',
    '@type': 'RealEstateAgent',
    name: 'Bali Real Estate',
    description: 'Licensed real estate agent specializing in Bali properties since 2019.',
    url: 'https://bali-real-estate.com',
    address: {
      '@type': 'PostalAddress',
      streetAddress: 'Jl. Pantai Berawa No. 123',
      addressLocality: 'Canggu',
      addressRegion: 'Badung',
      postalCode: '80361',
      addressCountry: 'ID'
    },
    areaServed: [
      'Canggu', 'Seminyak', 'Ubud', 'Sanur', 'Jimbaran', 'Pererenan'
    ],
    serviceType: [
      'Property Sales',
      'Long-term Rentals',
      'Investment Consultation',
      'Property Management',
      'Legal Documentation'
    ],
    knowsAbout: [
      'Bali Real Estate Market',
      'Foreign Property Investment',
      'Leasehold vs Freehold',
      'PMA Company Setup',
      'Property Valuation'
    ]
  };
}

function generatePropertyListingSchema(property: any) {
  return {
    '@context': 'https://schema.org',
    '@type': 'RealEstateListing',
    name: property.name,
    description: property.description,
    url: property.url,
    datePosted: property.datePosted,
    validThrough: property.validThrough,
    price: {
      '@type': 'PriceSpecification',
      price: property.price,
      priceCurrency: property.currency || 'USD'
    },
    listingAgent: {
      '@type': 'RealEstateAgent',
      name: 'Bali Real Estate'
    },
    address: {
      '@type': 'PostalAddress',
      addressLocality: property.location,
      addressRegion: 'Bali',
      addressCountry: 'ID'
    },
    floorSize: {
      '@type': 'QuantitativeValue',
      value: property.floorSize,
      unitCode: 'MTK'
    },
    numberOfRooms: property.bedrooms,
    numberOfBathroomsTotal: property.bathrooms
  };
}

export default AdvancedSchema;
