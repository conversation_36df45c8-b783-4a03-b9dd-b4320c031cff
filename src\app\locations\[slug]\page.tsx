import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import Link from 'next/link'
import { getLocationBySlug, getLocations } from '@/lib/payload'
import QuickContactForm from '@/components/forms/QuickContactForm'
import ComprehensiveLocationContent from '@/components/location/ComprehensiveLocationContent'
import { Breadcrumbs } from '@/components/common/Breadcrumbs'
import { InternalLinking } from '@/components/seo/InternalLinking'
import { CleanModernHero as ModernHero } from '@/components/ui/CleanModernHero'
import { motion } from '@/components/ui/MotionWrapper'
// AI components temporarily disabled

// Generate static params for all locations
export async function generateStaticParams() {
  const locations = await getLocations()
  return locations.map((location: any) => ({
    slug: location.slug,
  }))
}

// Generate metadata for each location
export async function generateMetadata({ params }: { params: Promise<{ slug: string }> }): Promise<Metadata> {
  const { slug } = await params
  const location = await getLocationBySlug(slug)
  
  if (!location) {
    return {
      title: 'Location Not Found',
    }
  }

  // Use custom meta description if available, otherwise generate from content
  const metaDescription = location.seoContent?.metaDescription ||
    location.description?.replace(/<[^>]*>/g, '').substring(0, 160) ||
    `Find long-term rentals and properties for sale in ${location.name}, Bali. Expert guidance for expats, digital nomads, and investors.`

  // Generate keywords from target keywords if available
  const keywords = location.seoContent?.targetKeywords?.map((k: any) => k.keyword).join(', ') ||
    `${location.name} Bali, ${location.name} rental, ${location.name} property, ${location.name} villa, Bali real estate`

  return {
    title: `${location.name} Long-term Rentals & Properties for Sale | Bali Real Estate`,
    description: metaDescription,
    keywords: keywords,
    openGraph: {
      title: `${location.name} - Bali Real Estate`,
      description: metaDescription,
      type: 'website',
    },
  }
}

export default async function LocationPage({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = await params
  const location = await getLocationBySlug(slug)
  
  if (!location) {
    notFound()
  }

  // Get background image based on location
  const getLocationBackgroundImage = (locationName: string) => {
    switch (locationName.toLowerCase()) {
      case 'canggu':
        return 'https://images.unsplash.com/photo-1571896349842-33c89424de2d?q=80&w=2080&auto=format&fit=crop'
      case 'ubud':
        return 'https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?q=80&w=2070&auto=format&fit=crop'
      case 'seminyak':
        return 'https://images.unsplash.com/photo-1540541338287-41700207dee6?q=80&w=2070&auto=format&fit=crop'
      case 'sanur':
        return 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?q=80&w=2070&auto=format&fit=crop'
      case 'jimbaran':
        return 'https://images.unsplash.com/photo-1566073771259-6a8506099945?q=80&w=2070&auto=format&fit=crop'
      case 'uluwatu':
        return 'https://images.unsplash.com/photo-1537953773345-d172ccf13cf1?q=80&w=2070&auto=format&fit=crop'
      default:
        return 'https://images.unsplash.com/photo-1537953773345-d172ccf13cf1?q=80&w=2070&auto=format&fit=crop'
    }
  }

  return (
    <main className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100">
      {/* AI Context temporarily disabled */}

      {/* Modern Breadcrumbs - PRD Requirement */}
      <div className="bg-gradient-to-r from-emerald-50 to-white py-6 border-b border-emerald-100">
        <div className="max-w-6xl mx-auto px-4">
          <Breadcrumbs />
        </div>
      </div>

      {/* Modern Hero Section */}
      <ModernHero
        title={`${location.name} Long-term Rentals & Properties`}
        subtitle={`${location.description} Discover premium properties and long-term rental opportunities in one of Bali's most sought-after locations with expert local guidance.`}
        backgroundImage={getLocationBackgroundImage(location.name)}
        primaryCTA={{
          text: "View Available Properties",
          href: `/chatbot?query=properties-in-${location.slug}`
        }}
        secondaryCTA={{
          text: "Get Location Guide",
          href: "/contact"
        }}
      />

      {/* Premium Navigation Menu */}
      <section className="bg-white/95 backdrop-blur-md py-6 sticky top-0 z-50 border-b border-emerald-100 shadow-xl">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-3 overflow-x-auto">
            <a href="#overview" className="whitespace-nowrap py-3 px-6 text-sm font-semibold text-gray-700 hover:text-emerald-600 hover:bg-emerald-50 rounded-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg">Overview</a>
            <a href="#neighborhoods" className="whitespace-nowrap py-3 px-6 text-sm font-semibold text-gray-700 hover:text-emerald-600 hover:bg-emerald-50 rounded-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg">Neighborhoods</a>
            <a href="#property-types" className="whitespace-nowrap py-3 px-6 text-sm font-semibold text-gray-700 hover:text-emerald-600 hover:bg-emerald-50 rounded-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg">Property Types</a>
            <a href="#lifestyle" className="whitespace-nowrap py-3 px-6 text-sm font-semibold text-gray-700 hover:text-emerald-600 hover:bg-emerald-50 rounded-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg">Lifestyle</a>
            <a href="#practical-info" className="whitespace-nowrap py-3 px-6 text-sm font-semibold text-gray-700 hover:text-emerald-600 hover:bg-emerald-50 rounded-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg">Practical Info</a>
            <a href="#faq" className="whitespace-nowrap py-3 px-6 text-sm font-semibold text-gray-700 hover:text-emerald-600 hover:bg-emerald-50 rounded-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg">FAQ</a>
          </nav>
        </div>
      </section>

      {/* Premium Location Overview */}
      <section id="overview" className="py-24 relative">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-br from-emerald-50/50 via-transparent to-teal-50/50" />
        <div className="absolute inset-0 opacity-40" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }} />

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
              <span className="text-gray-700">About</span>
              <br />
              <span className="bg-gradient-to-r from-emerald-600 to-emerald-700 bg-clip-text text-transparent">
                {location.name}
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Everything you need to know about living, investing, and thriving in {location.name}.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl p-8 md:p-12 border border-white/50"
          >
            <div className="prose prose-xl prose-emerald max-w-none">
              <div className="text-gray-700 leading-relaxed">
                {location.lifestyle ? (
                  <div dangerouslySetInnerHTML={{ __html: location.lifestyle }} />
                ) : (
                  <p className="text-xl leading-relaxed">
                    {location.name} is one of Bali's most sought-after locations for long-term rentals and property investment.
                    This area offers a unique blend of traditional Indonesian culture and modern amenities, making it perfect
                    for expats, digital nomads, and property investors looking for the authentic Bali experience.
                  </p>
                )}
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Premium Amenities Section */}
      {location.amenities && location.amenities.length > 0 && (
        <section className="py-24 relative">
          {/* Premium Background */}
          <div className="absolute inset-0 bg-gradient-to-br from-white via-gray-50 to-emerald-50/30" />
          <div className="absolute inset-0 opacity-50" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23000000' fill-opacity='0.03' fill-rule='evenodd'%3E%3Cpath d='m0 40l40-40h-40v40zm40 0v-40h-40l40 40z'/%3E%3C/g%3E%3C/svg%3E")`
          }} />

          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
                <span className="bg-gradient-to-r from-emerald-600 to-emerald-700 bg-clip-text text-transparent">
                  What Makes {location.name}
                </span>
                <br />
                <span className="text-gray-700">Special</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Discover the unique amenities and features that make {location.name} a premier destination for living and investing.
              </p>
            </motion.div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {location.amenities.map((amenity: any, index: number) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="group bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 border border-white/50 hover:border-emerald-200/50 transform hover:-translate-y-2"
                >
                  <div className="flex items-center">
                    <div className="w-4 h-4 bg-gradient-to-r from-emerald-400 to-teal-400 rounded-full mr-4 group-hover:scale-110 transition-transform duration-300"></div>
                    <span className="text-lg font-semibold text-gray-800 group-hover:text-emerald-700 transition-colors duration-300">{amenity.amenity}</span>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Premium Property Types & Pricing */}
      <section id="property-types" className="py-24 relative">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-br from-emerald-50/50 via-transparent to-teal-50/50" />
        <div className="absolute inset-0 opacity-40" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }} />

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
              <span className="bg-gradient-to-r from-emerald-600 to-emerald-700 bg-clip-text text-transparent">
                Property Types & Pricing
              </span>
              <br />
              <span className="text-gray-700">in {location.name}</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Comprehensive overview of available property types and current market pricing in {location.name}.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 gap-8 max-w-6xl mx-auto">
            {/* Villa Rentals */}
            {location.priceRanges?.villa && (
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="group bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 border border-white/50 hover:border-emerald-200/50 transform hover:-translate-y-2"
              >
                <div className="flex items-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-gray-800 group-hover:text-emerald-700 transition-colors duration-300">Villa Rentals</h3>
                    <span className="text-gray-500 text-sm">Long-term Options</span>
                  </div>
                </div>

                {location.priceRanges.villa.longTermRental && (
                  <div className="mb-6 p-4 bg-emerald-50 rounded-2xl">
                    <span className="text-sm font-medium text-emerald-600 uppercase tracking-wider">Monthly Range:</span>
                    <p className="text-3xl font-bold bg-gradient-to-r from-emerald-600 to-emerald-700 bg-clip-text text-transparent mt-2">
                      {location.priceRanges.villa.longTermRental}
                    </p>
                  </div>
                )}
                <p className="text-gray-600 leading-relaxed text-lg">
                  Private villas with pools, gardens, and modern amenities. Perfect for families and long-term stays in {location.name}.
                </p>
              </motion.div>
            )}

            {/* Villa Sales */}
            {location.priceRanges?.villa?.forSaleFreehold && (
              <motion.div
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="group bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 border border-white/50 hover:border-purple-200/50 transform hover:-translate-y-2"
              >
                <div className="flex items-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-gray-800 group-hover:text-purple-700 transition-colors duration-300">Villa Sales</h3>
                    <span className="text-gray-500 text-sm">Investment Opportunities</span>
                  </div>
                </div>

                <div className="mb-6 p-4 bg-purple-50 rounded-2xl">
                  <span className="text-sm font-medium text-purple-600 uppercase tracking-wider">Freehold Price Range:</span>
                  <p className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mt-2">
                    {location.priceRanges.villa.forSaleFreehold}
                  </p>
                </div>
                <p className="text-gray-600 leading-relaxed text-lg">
                  Own your piece of paradise with freehold villa ownership opportunities in {location.name}.
                </p>
              </motion.div>
            )}
          </div>
        </div>
      </section>

      {/* Premium Lifestyle & Amenities */}
      <section id="lifestyle" className="py-24 relative">
        {/* Premium Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-white via-gray-50 to-emerald-50/30" />
        <div className="absolute inset-0 opacity-50" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23000000' fill-opacity='0.03' fill-rule='evenodd'%3E%3Cpath d='m0 40l40-40h-40v40zm40 0v-40h-40l40 40z'/%3E%3C/g%3E%3C/svg%3E")`
        }} />

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
              <span className="bg-gradient-to-r from-emerald-600 to-emerald-700 bg-clip-text text-transparent">
                Lifestyle in
              </span>
              <br />
              <span className="text-gray-700">{location.name}</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Discover what makes living in {location.name} a unique and enriching experience.
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="space-y-8"
            >
              <div className="group bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 border border-white/50 hover:border-emerald-200/50">
                <div className="flex items-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-gray-800 group-hover:text-emerald-600 transition-colors duration-300">Living Experience</h3>
                    <span className="text-gray-500 text-sm">Authentic Bali Lifestyle</span>
                  </div>
                </div>
                <p className="text-gray-700 leading-relaxed text-lg">
                  {location.name} offers a unique lifestyle that perfectly balances modern conveniences with traditional Balinese culture.
                  The area is known for its welcoming expat community, excellent infrastructure, and diverse dining and entertainment options.
                </p>
              </div>

              <div className="group bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 border border-white/50 hover:border-blue-200/50">
                <div className="flex items-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-gray-800 group-hover:text-blue-600 transition-colors duration-300">Digital Nomad Friendly</h3>
                    <span className="text-gray-500 text-sm">Remote Work Paradise</span>
                  </div>
                </div>
                <p className="text-gray-700 leading-relaxed text-lg">
                  With reliable high-speed internet, numerous coworking spaces, and a thriving community of remote workers,
                  {location.name} has become a top destination for digital nomads and remote professionals.
                </p>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <div className="group bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 border border-white/50 hover:border-purple-200/50">
                <div className="flex items-center mb-8">
                  <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-gray-800 group-hover:text-purple-600 transition-colors duration-300">Key Amenities</h3>
                    <span className="text-gray-500 text-sm">Everything You Need</span>
                  </div>
                </div>

                <ul className="space-y-4">
                  {[
                    "High-speed internet (100+ Mbps available)",
                    "International restaurants and cafes",
                    "Coworking spaces and business centers",
                    "Healthcare facilities and pharmacies",
                    "Shopping centers and local markets",
                    "Fitness centers and yoga studios"
                  ].map((amenity, index) => (
                    <motion.li
                      key={index}
                      initial={{ opacity: 0, x: 20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      viewport={{ once: true }}
                      className="flex items-center group/item"
                    >
                      <div className="w-4 h-4 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full mr-4 group-hover/item:scale-110 transition-transform duration-300"></div>
                      <span className="text-gray-700 group-hover/item:text-purple-700 transition-colors duration-300 font-medium text-lg">{amenity}</span>
                    </motion.li>
                  ))}
                </ul>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Modern Transportation & Accessibility */}
      <section className="py-20 bg-gradient-to-br from-white to-emerald-50">
        <div className="max-w-6xl mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="bg-gradient-to-r from-emerald-600 to-emerald-700 bg-clip-text text-transparent">
                Transportation & Accessibility
              </span>
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-emerald-500 to-emerald-600 mx-auto rounded-full"></div>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
              className="group text-center bg-white rounded-3xl p-8 shadow-2xl hover:shadow-emerald-500/20 transition-all duration-500 transform hover:scale-105 border border-emerald-100"
            >
              <div className="w-20 h-20 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-3xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <span className="text-3xl">✈️</span>
              </div>
              <h3 className="text-xl font-bold mb-4 text-gray-800 group-hover:text-emerald-700 transition-colors duration-300">Airport Access</h3>
              <p className="text-gray-600 leading-relaxed">30-60 minutes to Ngurah Rai International Airport</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="group text-center bg-white rounded-3xl p-8 shadow-2xl hover:shadow-emerald-500/20 transition-all duration-500 transform hover:scale-105 border border-emerald-100"
            >
              <div className="w-20 h-20 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-3xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <span className="text-3xl">🛵</span>
              </div>
              <h3 className="text-xl font-bold mb-4 text-gray-800 group-hover:text-emerald-700 transition-colors duration-300">Local Transport</h3>
              <p className="text-gray-600 leading-relaxed">Scooter rentals, taxis, and ride-sharing available</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
              className="group text-center bg-white rounded-3xl p-8 shadow-2xl hover:shadow-emerald-500/20 transition-all duration-500 transform hover:scale-105 border border-emerald-100"
            >
              <div className="w-20 h-20 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-3xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <span className="text-3xl">🚶</span>
              </div>
              <h3 className="text-xl font-bold mb-4 text-gray-800 group-hover:text-emerald-700 transition-colors duration-300">Walkability</h3>
              <p className="text-gray-600 leading-relaxed">Many amenities within walking or short scooter distance</p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Modern FAQ Section */}
      <section id="faq" className="py-20 bg-gradient-to-br from-emerald-50 to-white">
        <div className="max-w-4xl mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="bg-gradient-to-r from-emerald-600 to-emerald-700 bg-clip-text text-transparent">
                Frequently Asked Questions
              </span>
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-emerald-500 to-emerald-600 mx-auto rounded-full"></div>
          </motion.div>

          <div className="space-y-6">
            {[
              {
                question: `What's the average cost of living in ${location.name}?`,
                answer: "Living costs vary depending on lifestyle, but expect $800-2000/month for a comfortable lifestyle including accommodation, food, and transportation."
              },
              {
                question: `How long can foreigners stay in ${location.name}?`,
                answer: "Tourists can stay up to 30 days visa-free, or get a 60-day visa on arrival. For longer stays, consider a B211/B212 visa or other long-term visa options."
              },
              {
                question: `Is it safe for foreigners to live in ${location.name}?`,
                answer: `${location.name} is generally very safe for foreigners, with a large expat community and good security. Standard travel precautions are recommended.`
              },
              {
                question: `What's the internet speed like in ${location.name}?`,
                answer: "Most areas offer reliable internet with speeds of 50-100+ Mbps, making it suitable for remote work and video calls."
              }
            ].map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="group bg-white rounded-3xl p-8 shadow-2xl hover:shadow-emerald-500/20 transition-all duration-500 transform hover:scale-[1.02] border border-emerald-100"
              >
                <h3 className="text-xl font-bold mb-4 text-gray-800 group-hover:text-emerald-700 transition-colors duration-300">
                  {faq.question}
                </h3>
                <p className="text-gray-600 leading-relaxed text-lg">
                  {faq.answer}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Modern Comprehensive Location Content */}
      <section className="py-20 bg-gradient-to-br from-white via-emerald-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="bg-gradient-to-r from-emerald-600 to-emerald-700 bg-clip-text text-transparent">
                Complete {location.name} Guide
              </span>
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-emerald-500 to-emerald-600 mx-auto rounded-full mb-6"></div>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Everything you need to know about living, investing, and thriving in {location.name}.
              From market analysis to practical living information.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="bg-white rounded-3xl shadow-2xl p-8 border border-emerald-100"
          >
            <ComprehensiveLocationContent locationSlug={location.slug} />
          </motion.div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="py-16 bg-gradient-to-r from-emerald-600 to-emerald-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="text-white">
              <h2 className="text-3xl font-bold mb-6">Ready to Explore {location.name}?</h2>
              <p className="text-xl mb-8 text-emerald-100">
                Get personalized recommendations and expert guidance for your {location.name} property search.
                Our local specialists know every neighborhood and can help you find the perfect match.
              </p>

              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0 w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span className="ml-3 text-emerald-100">Local {location.name} expertise</span>
                </div>

                <div className="flex items-center">
                  <div className="flex-shrink-0 w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span className="ml-3 text-emerald-100">Personalized property recommendations</span>
                </div>

                <div className="flex items-center">
                  <div className="flex-shrink-0 w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span className="ml-3 text-emerald-100">Free market analysis and consultation</span>
                </div>
              </div>

              <div className="mt-8">
                <Link
                  href="/locations"
                  className="inline-flex items-center text-emerald-200 hover:text-white font-medium"
                >
                  Compare Other Locations
                  <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                  </svg>
                </Link>
              </div>
            </div>

            <div>
              <QuickContactForm
                title={`Find Your Perfect ${location.name} Property`}
                subtitle="Get expert local guidance and recommendations"
                source={`location-page-${location.slug}`}
                locationSlug={location.slug}
              />
            </div>
          </div>
        </div>
      </section>

      {/* STORY-003: Internal Linking for SEO */}
      <InternalLinking
        currentPage="location"
        location={location.slug}
      />
    </main>
  )
}
