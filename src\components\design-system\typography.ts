/**
 * Design System - Typography
 * Bali Real Estate Website
 * 
 * This file defines the complete typography system including
 * font families, sizes, weights, and semantic text styles.
 */

// Font Families
export const fontFamilies = {
  sans: [
    'Inter',
    '-apple-system',
    'BlinkMacSystemFont',
    '"Segoe UI"',
    'Roboto',
    '"Helvetica Neue"',
    'Arial',
    'sans-serif',
  ],
  serif: [
    'Playfair Display',
    'Georgia',
    'Cambria',
    '"Times New Roman"',
    'Times',
    'serif',
  ],
  mono: [
    '"Fira Code"',
    'Consolas',
    '"Liberation Mono"',
    'Menlo',
    'Courier',
    'monospace',
  ],
} as const;

// Font Weights
export const fontWeights = {
  thin: '100',
  extralight: '200',
  light: '300',
  normal: '400',
  medium: '500',
  semibold: '600',
  bold: '700',
  extrabold: '800',
  black: '900',
} as const;

// Font Sizes (with line heights)
export const fontSizes = {
  xs: {
    fontSize: '0.75rem',    // 12px
    lineHeight: '1rem',     // 16px
  },
  sm: {
    fontSize: '0.875rem',   // 14px
    lineHeight: '1.25rem',  // 20px
  },
  base: {
    fontSize: '1rem',       // 16px
    lineHeight: '1.5rem',   // 24px
  },
  lg: {
    fontSize: '1.125rem',   // 18px
    lineHeight: '1.75rem',  // 28px
  },
  xl: {
    fontSize: '1.25rem',    // 20px
    lineHeight: '1.75rem',  // 28px
  },
  '2xl': {
    fontSize: '1.5rem',     // 24px
    lineHeight: '2rem',     // 32px
  },
  '3xl': {
    fontSize: '1.875rem',   // 30px
    lineHeight: '2.25rem',  // 36px
  },
  '4xl': {
    fontSize: '2.25rem',    // 36px
    lineHeight: '2.5rem',   // 40px
  },
  '5xl': {
    fontSize: '3rem',       // 48px
    lineHeight: '1',        // 48px
  },
  '6xl': {
    fontSize: '3.75rem',    // 60px
    lineHeight: '1',        // 60px
  },
  '7xl': {
    fontSize: '4.5rem',     // 72px
    lineHeight: '1',        // 72px
  },
  '8xl': {
    fontSize: '6rem',       // 96px
    lineHeight: '1',        // 96px
  },
  '9xl': {
    fontSize: '8rem',       // 128px
    lineHeight: '1',        // 128px
  },
} as const;

// Letter Spacing
export const letterSpacing = {
  tighter: '-0.05em',
  tight: '-0.025em',
  normal: '0em',
  wide: '0.025em',
  wider: '0.05em',
  widest: '0.1em',
} as const;

// Semantic Typography Styles
export const textStyles = {
  // Display styles (for hero sections, large headings)
  display: {
    large: {
      fontFamily: fontFamilies.serif.join(', '),
      fontSize: fontSizes['6xl'].fontSize,
      lineHeight: fontSizes['6xl'].lineHeight,
      fontWeight: fontWeights.bold,
      letterSpacing: letterSpacing.tight,
    },
    medium: {
      fontFamily: fontFamilies.serif.join(', '),
      fontSize: fontSizes['5xl'].fontSize,
      lineHeight: fontSizes['5xl'].lineHeight,
      fontWeight: fontWeights.bold,
      letterSpacing: letterSpacing.tight,
    },
    small: {
      fontFamily: fontFamilies.serif.join(', '),
      fontSize: fontSizes['4xl'].fontSize,
      lineHeight: fontSizes['4xl'].lineHeight,
      fontWeight: fontWeights.semibold,
      letterSpacing: letterSpacing.tight,
    },
  },

  // Heading styles
  heading: {
    h1: {
      fontFamily: fontFamilies.serif.join(', '),
      fontSize: fontSizes['3xl'].fontSize,
      lineHeight: fontSizes['3xl'].lineHeight,
      fontWeight: fontWeights.bold,
      letterSpacing: letterSpacing.tight,
    },
    h2: {
      fontFamily: fontFamilies.serif.join(', '),
      fontSize: fontSizes['2xl'].fontSize,
      lineHeight: fontSizes['2xl'].lineHeight,
      fontWeight: fontWeights.semibold,
      letterSpacing: letterSpacing.tight,
    },
    h3: {
      fontFamily: fontFamilies.sans.join(', '),
      fontSize: fontSizes.xl.fontSize,
      lineHeight: fontSizes.xl.lineHeight,
      fontWeight: fontWeights.semibold,
      letterSpacing: letterSpacing.normal,
    },
    h4: {
      fontFamily: fontFamilies.sans.join(', '),
      fontSize: fontSizes.lg.fontSize,
      lineHeight: fontSizes.lg.lineHeight,
      fontWeight: fontWeights.semibold,
      letterSpacing: letterSpacing.normal,
    },
    h5: {
      fontFamily: fontFamilies.sans.join(', '),
      fontSize: fontSizes.base.fontSize,
      lineHeight: fontSizes.base.lineHeight,
      fontWeight: fontWeights.semibold,
      letterSpacing: letterSpacing.normal,
    },
    h6: {
      fontFamily: fontFamilies.sans.join(', '),
      fontSize: fontSizes.sm.fontSize,
      lineHeight: fontSizes.sm.lineHeight,
      fontWeight: fontWeights.semibold,
      letterSpacing: letterSpacing.wide,
    },
  },

  // Body text styles
  body: {
    large: {
      fontFamily: fontFamilies.sans.join(', '),
      fontSize: fontSizes.lg.fontSize,
      lineHeight: fontSizes.lg.lineHeight,
      fontWeight: fontWeights.normal,
      letterSpacing: letterSpacing.normal,
    },
    medium: {
      fontFamily: fontFamilies.sans.join(', '),
      fontSize: fontSizes.base.fontSize,
      lineHeight: fontSizes.base.lineHeight,
      fontWeight: fontWeights.normal,
      letterSpacing: letterSpacing.normal,
    },
    small: {
      fontFamily: fontFamilies.sans.join(', '),
      fontSize: fontSizes.sm.fontSize,
      lineHeight: fontSizes.sm.lineHeight,
      fontWeight: fontWeights.normal,
      letterSpacing: letterSpacing.normal,
    },
  },

  // Caption and label styles
  caption: {
    fontFamily: fontFamilies.sans.join(', '),
    fontSize: fontSizes.xs.fontSize,
    lineHeight: fontSizes.xs.lineHeight,
    fontWeight: fontWeights.normal,
    letterSpacing: letterSpacing.wide,
  },

  label: {
    fontFamily: fontFamilies.sans.join(', '),
    fontSize: fontSizes.sm.fontSize,
    lineHeight: fontSizes.sm.lineHeight,
    fontWeight: fontWeights.medium,
    letterSpacing: letterSpacing.wide,
  },

  // Button text styles
  button: {
    large: {
      fontFamily: fontFamilies.sans.join(', '),
      fontSize: fontSizes.base.fontSize,
      lineHeight: fontSizes.base.lineHeight,
      fontWeight: fontWeights.semibold,
      letterSpacing: letterSpacing.wide,
    },
    medium: {
      fontFamily: fontFamilies.sans.join(', '),
      fontSize: fontSizes.sm.fontSize,
      lineHeight: fontSizes.sm.lineHeight,
      fontWeight: fontWeights.semibold,
      letterSpacing: letterSpacing.wide,
    },
    small: {
      fontFamily: fontFamilies.sans.join(', '),
      fontSize: fontSizes.xs.fontSize,
      lineHeight: fontSizes.xs.lineHeight,
      fontWeight: fontWeights.semibold,
      letterSpacing: letterSpacing.wider,
    },
  },
} as const;

// Responsive typography utilities
export const responsiveText = {
  // Hero text that scales with screen size
  hero: {
    mobile: textStyles.display.small,
    tablet: textStyles.display.medium,
    desktop: textStyles.display.large,
  },

  // Section headings that scale
  sectionHeading: {
    mobile: textStyles.heading.h2,
    tablet: textStyles.heading.h1,
    desktop: textStyles.heading.h1,
  },

  // Body text that scales
  bodyText: {
    mobile: textStyles.body.small,
    tablet: textStyles.body.medium,
    desktop: textStyles.body.medium,
  },
} as const;

// Typography utility functions
export const typographyUtils = {
  /**
   * Get CSS properties for a text style
   */
  getTextStyle: (style: keyof typeof textStyles | string) => {
    // This would be used in styled-components or CSS-in-JS
    return textStyles[style as keyof typeof textStyles] || textStyles.body.medium;
  },

  /**
   * Generate Tailwind CSS classes for text styles
   */
  getTailwindClasses: (style: keyof typeof textStyles) => {
    const styleObj = textStyles[style as keyof typeof textStyles];
    if (!styleObj) return '';

    // Convert style object to Tailwind classes
    // This is a simplified version - in practice, you'd map to actual Tailwind classes
    return `text-base font-normal leading-normal`;
  },
} as const;

// Export for Tailwind CSS configuration
export const tailwindTypography = {
  fontFamily: fontFamilies,
  fontSize: Object.fromEntries(
    Object.entries(fontSizes).map(([key, value]) => [
      key,
      [value.fontSize, { lineHeight: value.lineHeight }]
    ])
  ),
  fontWeight: fontWeights,
  letterSpacing,
} as const;

export type FontSize = keyof typeof fontSizes;
export type FontWeight = keyof typeof fontWeights;
export type TextStyle = keyof typeof textStyles;
