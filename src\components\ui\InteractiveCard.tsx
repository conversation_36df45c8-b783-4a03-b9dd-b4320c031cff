/**
 * Interactive Card Component with Hover Effects
 * STORY-008: UI/UX Transformation
 */

'use client';

import { motion } from '@/components/ui/SimpleMotion';
import Link from 'next/link';
import { ReactNode } from 'react';

interface Badge {
  text: string;
  color: 'emerald' | 'blue' | 'purple' | 'orange' | 'pink';
  icon?: ReactNode;
}

interface InteractiveCardProps {
  title: string;
  description: string;
  image?: string;
  href: string;
  badges?: Badge[];
  stats?: Array<{
    label: string;
    value: string;
  }>;
  className?: string;
  priority?: 'high' | 'medium' | 'low';
}

const badgeColors = {
  emerald: 'bg-emerald-100 text-emerald-700 border-emerald-200',
  blue: 'bg-blue-100 text-blue-700 border-blue-200',
  purple: 'bg-purple-100 text-purple-700 border-purple-200',
  orange: 'bg-orange-100 text-orange-700 border-orange-200',
  pink: 'bg-pink-100 text-pink-700 border-pink-200',
};

const priorityStyles = {
  high: 'ring-2 ring-emerald-200 shadow-emerald-100',
  medium: 'ring-1 ring-blue-200 shadow-blue-50',
  low: 'ring-1 ring-gray-200 shadow-gray-50',
};

export function InteractiveCard({
  title,
  description,
  image,
  href,
  badges = [],
  stats = [],
  className = '',
  priority = 'medium'
}: InteractiveCardProps) {
  const cardVariants = {
    initial: {
      scale: 1,
      y: 0,
      rotateX: 0,
      rotateY: 0,
    },
    hover: {
      scale: 1.02,
      y: -8,
      rotateX: 5,
      rotateY: 5,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    },
    tap: {
      scale: 0.98,
      transition: {
        duration: 0.1
      }
    }
  };

  const imageVariants = {
    initial: { scale: 1 },
    hover: {
      scale: 1.1,
      transition: {
        duration: 0.4,
        ease: "easeOut"
      }
    }
  };

  const contentVariants = {
    initial: { opacity: 0.8 },
    hover: {
      opacity: 1,
      transition: {
        duration: 0.2
      }
    }
  };

  return (
    <motion.div
      variants={cardVariants}
      initial="initial"
      whileHover="hover"
      whileTap="tap"
      className={`group relative ${className}`}
      style={{ perspective: '1000px' }}
    >
      <Link href={href} className="block">
        <div className={`
          relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden
          ${priorityStyles[priority]}
          transform-gpu
        `}>
          {/* Priority Indicator */}
          {priority === 'high' && (
            <div className="absolute top-4 right-4 z-10">
              <div className="bg-emerald-500 text-white px-3 py-1 rounded-full text-xs font-semibold flex items-center">
                <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                High Priority
              </div>
            </div>
          )}

          {/* Image Section */}
          {image && (
            <div className="relative h-48 sm:h-56 overflow-hidden">
              <motion.div
                variants={imageVariants}
                className="w-full h-full bg-cover bg-center"
                style={{ backgroundImage: `url(${image})` }}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
            </div>
          )}

          {/* Content Section */}
          <motion.div variants={contentVariants} className="p-6">
            {/* Badges */}
            {badges.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-4">
                {badges.map((badge, index) => (
                  <motion.div
                    key={index}
                    className={`
                      inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border
                      ${badgeColors[badge.color]}
                      transform transition-transform duration-200 group-hover:scale-105
                    `}
                    whileHover={{ scale: 1.1 }}
                  >
                    {badge.icon && <span className="mr-1">{badge.icon}</span>}
                    {badge.text}
                  </motion.div>
                ))}
              </div>
            )}

            {/* Title */}
            <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-emerald-600 transition-colors duration-200">
              {title}
            </h3>

            {/* Description */}
            <p className="text-gray-600 mb-4 leading-relaxed">
              {description}
            </p>

            {/* Stats */}
            {stats.length > 0 && (
              <div className="grid grid-cols-2 gap-4 mb-4">
                {stats.map((stat, index) => (
                  <div key={index} className="text-center">
                    <div className="text-2xl font-bold text-emerald-600">{stat.value}</div>
                    <div className="text-sm text-gray-500">{stat.label}</div>
                  </div>
                ))}
              </div>
            )}

            {/* CTA Arrow */}
            <div className="flex items-center justify-between">
              <span className="text-emerald-600 font-semibold text-sm group-hover:text-emerald-700 transition-colors">
                Explore →
              </span>
              
              {/* Animated Arrow */}
              <motion.div
                className="w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center group-hover:bg-emerald-200 transition-colors duration-200"
                whileHover={{ x: 4 }}
                transition={{ duration: 0.2 }}
              >
                <svg className="w-4 h-4 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </motion.div>
            </div>
          </motion.div>

          {/* Hover Glow Effect */}
          <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-emerald-400/0 via-emerald-400/5 to-blue-400/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
        </div>
      </Link>
    </motion.div>
  );
}

// Icon components for badges
export const LocationIcon = () => (
  <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
    <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
  </svg>
);

export const StarIcon = () => (
  <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
  </svg>
);

export const TrendingIcon = () => (
  <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
    <path fillRule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clipRule="evenodd" />
  </svg>
);

export default InteractiveCard;
