// AI Components
export { ChatWidget } from './ChatWidget/ChatWidget'
export { ChatBubble } from './ChatWidget/ChatBubble'
export { ChatInput } from './ChatWidget/ChatInput'
export { TypingIndicator } from './ChatWidget/TypingIndicator'
export { AIContextSetter } from './AIContextSetter'
export { ClientChatWidget } from './ClientChatWidget'

// AI Hooks
export { useConversation } from './hooks/useConversation'
export { useSessionManager } from './hooks/useSessionManager'

// AI Context
export { AIContextProvider, useAIContext } from './AIContext'
export type {
  ChatMessage,
  UserPreferences,
  PropertyMatch,
  LeadScore,
  AIContextState,
  AIAction
} from './AIContext'
