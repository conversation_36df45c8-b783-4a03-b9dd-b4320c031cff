<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Header Dropdown Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .dropdown-content {
            transition: all 0.2s ease-in-out;
        }
        .dropdown-content.hidden {
            opacity: 0;
            visibility: hidden;
            transform: translateY(-8px);
        }
        .dropdown-content.visible {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto p-8">
        <h1 class="text-3xl font-bold mb-8">Header Dropdown Test</h1>
        
        <!-- Test Desktop Dropdown -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold mb-4">Desktop Dropdown Test</h2>
            <div class="relative group">
                <button 
                    id="desktop-dropdown-btn"
                    class="flex items-center space-x-1 px-3 py-2 text-sm font-medium transition-colors text-neutral-700 hover:text-emerald-600 bg-white border rounded-lg"
                >
                    <span>Locations</span>
                    <svg class="w-4 h-4 transition-transform group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                
                <div 
                    id="desktop-dropdown-content"
                    class="dropdown-content hidden absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-neutral-200 py-2 z-10"
                >
                    <a href="#" class="block px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100">All Locations</a>
                    <a href="#" class="block px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100">Canggu</a>
                    <a href="#" class="block px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100">Ubud</a>
                    <a href="#" class="block px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100">Seminyak</a>
                </div>
            </div>
        </div>
        
        <!-- Test Mobile Dropdown -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold mb-4">Mobile Dropdown Test</h2>
            <div>
                <button 
                    id="mobile-dropdown-btn"
                    class="flex items-center justify-between w-full px-3 py-2 text-left text-base font-medium transition-colors text-neutral-700 hover:text-emerald-600 bg-white border rounded-lg"
                >
                    <span>Property Types</span>
                    <svg 
                        id="mobile-dropdown-arrow"
                        class="w-4 h-4 transition-transform"
                        fill="none" 
                        stroke="currentColor" 
                        viewBox="0 0 24 24"
                    >
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                
                <div 
                    id="mobile-dropdown-content"
                    class="overflow-hidden transition-all duration-200 max-h-0"
                >
                    <div class="pl-6 space-y-1 pt-2">
                        <a href="#" class="block px-3 py-2 text-sm text-neutral-600 hover:text-emerald-600">All Property Types</a>
                        <a href="#" class="block px-3 py-2 text-sm text-neutral-600 hover:text-emerald-600">Villas</a>
                        <a href="#" class="block px-3 py-2 text-sm text-neutral-600 hover:text-emerald-600">Guesthouses</a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Results -->
        <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-4">Test Results</h2>
            <div id="test-results" class="space-y-2">
                <p class="text-gray-600">Click the dropdowns above to test functionality...</p>
            </div>
        </div>
    </div>

    <script>
        // Test Results Logger
        function logResult(test, status, message) {
            const results = document.getElementById('test-results');
            const result = document.createElement('div');
            result.className = `p-2 rounded ${status === 'pass' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`;
            result.innerHTML = `<strong>${test}:</strong> ${status.toUpperCase()} - ${message}`;
            results.appendChild(result);
        }

        // Desktop Dropdown Test
        const desktopBtn = document.getElementById('desktop-dropdown-btn');
        const desktopContent = document.getElementById('desktop-dropdown-content');
        let desktopOpen = false;

        desktopBtn.addEventListener('mouseenter', () => {
            desktopContent.classList.remove('hidden');
            desktopContent.classList.add('visible');
            desktopOpen = true;
            logResult('Desktop Hover Open', 'pass', 'Dropdown opens on hover');
        });

        desktopBtn.addEventListener('mouseleave', () => {
            setTimeout(() => {
                if (!desktopContent.matches(':hover')) {
                    desktopContent.classList.remove('visible');
                    desktopContent.classList.add('hidden');
                    desktopOpen = false;
                    logResult('Desktop Hover Close', 'pass', 'Dropdown closes when not hovering');
                }
            }, 100);
        });

        desktopContent.addEventListener('mouseleave', () => {
            desktopContent.classList.remove('visible');
            desktopContent.classList.add('hidden');
            desktopOpen = false;
            logResult('Desktop Content Leave', 'pass', 'Dropdown closes when leaving content');
        });

        // Mobile Dropdown Test
        const mobileBtn = document.getElementById('mobile-dropdown-btn');
        const mobileContent = document.getElementById('mobile-dropdown-content');
        const mobileArrow = document.getElementById('mobile-dropdown-arrow');
        let mobileOpen = false;

        mobileBtn.addEventListener('click', () => {
            mobileOpen = !mobileOpen;
            
            if (mobileOpen) {
                mobileContent.style.maxHeight = '12rem';
                mobileArrow.style.transform = 'rotate(180deg)';
                logResult('Mobile Click Open', 'pass', 'Mobile dropdown opens on click');
            } else {
                mobileContent.style.maxHeight = '0';
                mobileArrow.style.transform = 'rotate(0deg)';
                logResult('Mobile Click Close', 'pass', 'Mobile dropdown closes on click');
            }
        });

        // Initial test
        setTimeout(() => {
            logResult('JavaScript Load', 'pass', 'Test script loaded successfully');
        }, 100);
    </script>
</body>
</html>
