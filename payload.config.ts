import { buildConfig } from 'payload'
import { postgresAdapter } from '@payloadcms/db-postgres'
import { lexicalEditor } from '@payloadcms/richtext-lexical'
import { seoPlugin } from '@payloadcms/plugin-seo'
import { formBuilderPlugin } from '@payloadcms/plugin-form-builder'
import path from 'path'
import dotenv from 'dotenv'
import sharp from 'sharp'

// Import collections
import { Users } from './src/collections/Users'
import { Media } from './src/collections/Media'
import { Leads } from './src/collections/Leads'
import { Locations } from './src/collections/Locations'
import { PropertyTypes } from './src/collections/PropertyTypes'
// import { AIConversations } from './src/collections/AIConversations'
// import { PropertyMatches } from './src/collections/PropertyMatches'

dotenv.config({ path: '.env.local' })

export default buildConfig({
  admin: {
    user: 'users',
    bundler: 'webpack',
  },
  sharp,
  serverURL: process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:3000',
  collections: [Users, Media, Leads, Locations, PropertyTypes], // AIConversations, PropertyMatches - disabled for Story 1.2 rollback
  editor: lexicalEditor({}),
  plugins: [
    seoPlugin({
      collections: ['locations', 'property-types'],
      uploadsCollection: 'media',
    }),
    formBuilderPlugin({
      fields: {
        payment: false,
      },
    }),
  ],
  secret: process.env.PAYLOAD_SECRET || 'your-secret-here',
  typescript: {
    outputFile: './src/payload-types.ts',
  },
  db: postgresAdapter({
    pool: {
      connectionString: process.env.SUPABASE_DATABASE_URL,
      max: 5, // Maximum number of connections
      min: 1, // Minimum number of connections
      idleTimeoutMillis: 10000, // Close idle connections after 10 seconds
      connectionTimeoutMillis: 5000, // Timeout after 5 seconds
    },
    schemaName: 'payload_cms',
    push: false, // Disable automatic schema pushing in development
  }),
})
