import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '../../../../payload.config'

export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const body = await request.json()

    // Validate required fields
    if (!body.name || !body.email || !body.message || !body.gdprConsent) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Process location and property type relationships
    let preferredLocations: string[] = []
    let propertyTypes: string[] = []

    if (body.propertyPreferences?.preferredLocations) {
      // Convert location slugs to IDs
      for (const locationSlug of body.propertyPreferences.preferredLocations) {
        try {
          const locationResult = await payload.find({
            collection: 'locations',
            where: {
              slug: {
                equals: locationSlug,
              },
            },
            limit: 1,
          })
          
          if (locationResult.docs.length > 0) {
            preferredLocations.push(locationResult.docs[0].id)
          }
        } catch (error) {
          console.error(`Error finding location ${locationSlug}:`, error)
        }
      }
    }

    if (body.propertyPreferences?.propertyType) {
      // Convert property type slugs to IDs
      for (const propertyTypeSlug of body.propertyPreferences.propertyType) {
        try {
          const propertyTypeResult = await payload.find({
            collection: 'property-types',
            where: {
              slug: {
                equals: propertyTypeSlug,
              },
            },
            limit: 1,
          })
          
          if (propertyTypeResult.docs.length > 0) {
            propertyTypes.push(propertyTypeResult.docs[0].id)
          }
        } catch (error) {
          console.error(`Error finding property type ${propertyTypeSlug}:`, error)
        }
      }
    }

    // Prepare the lead data
    const leadData = {
      name: body.name,
      email: body.email,
      phone: body.phone || '',
      message: body.message,
      leadType: body.leadType || 'general',
      source: body.source || 'contact-form',
      status: 'new',
      timeline: body.timeline,
      budget: body.budget ? {
        budgetRange: body.budget.budgetRange,
        budgetFlexible: body.budget.budgetFlexible || false,
      } : undefined,
      propertyPreferences: {
        preferredLocations: preferredLocations,
        propertyType: propertyTypes,
        transactionType: body.propertyPreferences?.transactionType || 'undecided',
      },
      personalInfo: body.personalInfo ? {
        currentLocation: body.personalInfo.currentLocation,
        occupation: body.personalInfo.occupation,
        familySize: body.personalInfo.familySize,
        previousBaliExperience: body.personalInfo.previousBaliExperience || false,
      } : undefined,
      emailOptIn: body.emailOptIn || false,
      gdprConsent: body.gdprConsent,
      lastContactDate: new Date().toISOString(),
    }

    // Create the lead in Payload CMS
    const result = await payload.create({
      collection: 'leads',
      data: leadData,
    })

    // Send notification email (optional - implement based on your email service)
    try {
      await sendNotificationEmail(result)
    } catch (emailError) {
      console.error('Failed to send notification email:', emailError)
      // Don't fail the request if email fails
    }

    return NextResponse.json(
      { 
        success: true, 
        leadId: result.id,
        message: 'Lead created successfully' 
      },
      { status: 201 }
    )

  } catch (error) {
    console.error('Error creating lead:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const { searchParams } = new URL(request.url)
    
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const status = searchParams.get('status')
    const leadType = searchParams.get('leadType')

    const where: any = {}
    
    if (status) {
      where.status = { equals: status }
    }
    
    if (leadType) {
      where.leadType = { equals: leadType }
    }

    const result = await payload.find({
      collection: 'leads',
      where,
      page,
      limit,
      sort: '-createdAt',
    })

    return NextResponse.json(result)

  } catch (error) {
    console.error('Error fetching leads:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Helper function to send notification emails
async function sendNotificationEmail(lead: any) {
  // This is a placeholder for email notification
  // You can integrate with services like:
  // - Resend
  // - SendGrid
  // - Nodemailer
  // - AWS SES
  
  console.log('📧 New lead notification:', {
    name: lead.name,
    email: lead.email,
    leadType: lead.leadType,
    leadScore: lead.leadScore,
    timeline: lead.timeline,
  })

  // Example implementation with a hypothetical email service:
  /*
  const emailService = new EmailService()
  
  await emailService.send({
    to: process.env.NOTIFICATION_EMAIL,
    subject: `New Lead: ${lead.name} (Score: ${lead.leadScore})`,
    template: 'new-lead-notification',
    data: {
      lead,
      adminUrl: `${process.env.NEXT_PUBLIC_SERVER_URL}/admin/collections/leads/${lead.id}`,
    },
  })

  // Send auto-response to the lead
  await emailService.send({
    to: lead.email,
    subject: 'Thank you for your inquiry - Bali Real Estate',
    template: 'lead-auto-response',
    data: {
      name: lead.name,
      leadType: lead.leadType,
    },
  })
  */
}
