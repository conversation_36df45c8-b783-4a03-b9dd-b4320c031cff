import { getPayload } from 'payload'
import config from '../../payload.config'

const updateGuesthousePropertyType = async (): Promise<void> => {
  try {
    const payload = await getPayload({ config })

    console.log('🏠 Updating Guesthouse property type with comprehensive content...')

    // Find existing guesthouse property type
    const existing = await payload.find({
      collection: 'property-types',
      where: {
        slug: {
          equals: 'guesthouse'
        }
      }
    })

    if (existing.docs.length === 0) {
      console.log('❌ Guesthouse property type not found')
      return
    }

    const guesthouseId = existing.docs[0].id

    // Comprehensive guesthouse data
    const guesthouseUpdateData = {
      name: 'Guesthouse',
      slug: 'guesthouse',
      category: 'both',
      description: {
        root: {
          type: 'root',
          children: [
            {
              type: 'paragraph',
              children: [
                {
                  type: 'text',
                  text: 'Authentic guesthouses in Bali offer charming, budget-friendly accommodations that provide an intimate glimpse into local Indonesian culture. These cozy properties combine traditional Balinese hospitality with modern comfort, perfect for travelers seeking genuine cultural experiences and community connections.'
                }
              ]
            }
          ]
        }
      },
      detailedDescription: {
        root: {
          type: 'root',
          children: [
            {
              type: 'paragraph',
              children: [
                {
                  type: 'text',
                  text: 'Guesthouse living in Bali represents an authentic and affordable way to experience the island\'s rich culture and warm hospitality. These intimate properties, often family-owned and operated, typically feature 3-10 rooms with shared or private bathrooms, communal areas for socializing, and traditional Balinese architectural elements. Many guesthouses are located in residential neighborhoods, providing guests with genuine insights into local daily life, from morning temple ceremonies to evening family gatherings. The guesthouse experience emphasizes community and cultural exchange, with hosts often serving as informal guides to local attractions, hidden gems, and authentic dining experiences. These properties cater to budget-conscious travelers, digital nomads seeking affordable long-term stays, and cultural enthusiasts who value authentic experiences over luxury amenities.'
                }
              ]
            }
          ]
        }
      },
      typicalFeatures: [
        { feature: 'Shared or private bathroom', description: 'Clean, modern bathrooms with hot water and basic amenities' },
        { feature: 'Basic kitchen facilities', description: 'Shared kitchen or kitchenette with refrigerator and cooking basics' },
        { feature: 'WiFi included', description: 'Reliable internet connection throughout the property' },
        { feature: 'Motorbike parking', description: 'Secure parking area for scooters and motorbikes' },
        { feature: 'Local neighborhood setting', description: 'Authentic residential area with local shops and warungs nearby' },
        { feature: 'Air conditioning or fan', description: 'Climate control options in rooms' },
        { feature: 'Common areas', description: 'Shared spaces for relaxation and socializing with other guests' },
        { feature: 'Local host assistance', description: 'Friendly hosts providing local recommendations and support' },
        { feature: 'Laundry service', description: 'Washing facilities or affordable laundry service' },
        { feature: 'Traditional breakfast', description: 'Optional Indonesian breakfast featuring local specialties' }
      ],
      priceRanges: {
        rental: {
          shortTerm: '$15-60/night',
          longTerm: '$300-1200/month'
        },
        purchase: {
          freehold: '$50k-300k',
          leasehold: '$30k-200k'
        }
      },
      sizeRanges: {
        bedroomsMin: 1,
        bedroomsMax: 3,
        areaMin: 15,
        areaMax: 80
      },
      targetAudience: [
        { audience: 'budget-travelers' },
        { audience: 'digital-nomads' },
        { audience: 'property-investors' }
      ],
      rentalTerms: {
        root: {
          type: 'root',
          children: [
            {
              type: 'paragraph',
              children: [
                {
                  type: 'text',
                  text: 'Guesthouse rentals in Bali typically require minimal deposits (1-4 weeks rent) and offer flexible booking terms. Most properties include basic utilities like electricity, water, and WiFi in the rental price. Long-term stays (1+ months) often receive significant discounts and may include additional services like laundry, breakfast, or motorbike rental. Many guesthouses operate on a community-style basis with shared responsibilities and house rules that promote respectful coexistence among guests.'
                }
              ]
            }
          ]
        }
      },
      purchaseProcess: {
        root: {
          type: 'root',
          children: [
            {
              type: 'paragraph',
              children: [
                {
                  type: 'text',
                  text: 'Purchasing a guesthouse in Bali offers an excellent entry point into the Indonesian property market. These properties can be acquired through leasehold arrangements or Indonesian company structures, often at significantly lower costs than villas or apartments. The purchase process involves evaluating existing guest reviews, occupancy rates, and local competition. Many guesthouses come with established booking platform presence and repeat guest relationships, providing immediate income potential for new owners.'
                }
              ]
            }
          ]
        }
      },
      investmentAnalysis: {
        rentalYield: '12-20% annually',
        appreciationRate: '6-10% per year',
        investmentHighlights: [
          { highlight: 'Lower entry cost compared to villas and apartments' },
          { highlight: 'High occupancy rates due to budget traveler demand' },
          { highlight: 'Strong community and repeat guest relationships' },
          { highlight: 'Minimal maintenance and operational complexity' },
          { highlight: 'Opportunity for owner-operator lifestyle business' },
          { highlight: 'Growing digital nomad market seeking affordable long-term stays' }
        ]
      },
      faq: [
        {
          question: 'What makes guesthouses different from hotels or villas?',
          answer: {
            root: {
              type: 'root',
              children: [
                {
                  type: 'paragraph',
                  children: [
                    {
                      type: 'text',
                      text: 'Guesthouses offer a more intimate, community-focused experience with personal attention from local hosts. Unlike hotels, they provide authentic cultural immersion and neighborhood integration. Compared to villas, they offer affordability and social interaction while maintaining privacy and comfort.'
                    }
                  ]
                }
              ]
            }
          }
        },
        {
          question: 'Are guesthouses suitable for long-term stays?',
          answer: {
            root: {
              type: 'root',
              children: [
                {
                  type: 'paragraph',
                  children: [
                    {
                      type: 'text',
                      text: 'Yes, many guesthouses cater specifically to digital nomads and long-term travelers, offering monthly rates, extended stay discounts, and additional services like workspace areas, laundry facilities, and local integration support.'
                    }
                  ]
                }
              ]
            }
          }
        },
        {
          question: 'What should I consider when investing in a guesthouse?',
          answer: {
            root: {
              type: 'root',
              children: [
                {
                  type: 'paragraph',
                  children: [
                    {
                      type: 'text',
                      text: 'Key factors include location accessibility, local competition, existing guest reviews and bookings, property condition, and potential for expansion or improvement. Consider whether you want to operate it personally or hire local management, as this significantly impacts profitability and lifestyle requirements.'
                    }
                  ]
                }
              ]
            }
          }
        }
      ]
    }

    // Update the guesthouse property type
    const updated = await payload.update({
      collection: 'property-types',
      id: guesthouseId,
      data: guesthouseUpdateData,
    })

    console.log(`✅ Updated guesthouse property type: ${updated.id}`)
    console.log('🎉 Guesthouse property type updated successfully!')
    
  } catch (error) {
    console.error('❌ Error updating guesthouse property type:', error)
    throw error
  }
}

// Run if called directly
if (require.main === module) {
  updateGuesthousePropertyType()
    .then(() => {
      console.log('✅ Script completed successfully')
      process.exit(0)
    })
    .catch((error) => {
      console.error('❌ Script failed:', error)
      process.exit(1)
    })
}

export { updateGuesthousePropertyType }
