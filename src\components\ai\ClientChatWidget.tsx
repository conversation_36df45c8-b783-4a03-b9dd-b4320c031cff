'use client'

import dynamic from 'next/dynamic'
import { Suspense } from 'react'

// Lazy load ChatWidget with proper client-side handling
const ChatWidget = dynamic(() => import('./ChatWidget/ChatWidget').then(mod => ({ default: mod.ChatWidget })), {
  ssr: false,
  loading: () => null
})

export const ClientChatWidget: React.FC = () => {
  return (
    <Suspense fallback={null}>
      <ChatWidget />
    </Suspense>
  )
}
