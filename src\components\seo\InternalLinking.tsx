/**
 * Internal Linking Strategy Component
 * STORY-003: Advanced SEO & Content Optimization
 */

import Link from 'next/link';

interface RelatedLink {
  title: string;
  href: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
}

interface InternalLinkingProps {
  currentPage: string;
  location?: string;
  propertyType?: string;
  transactionType?: string;
  className?: string;
}

export function InternalLinking({
  currentPage,
  location,
  propertyType,
  transactionType,
  className = ''
}: InternalLinkingProps) {
  const relatedLinks = generateRelatedLinks(currentPage, location, propertyType, transactionType);

  if (relatedLinks.length === 0) return null;

  return (
    <section className={`py-12 bg-gray-50 ${className}`}>
      <div className="max-w-6xl mx-auto px-4">
        <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
          Related Pages
        </h2>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {relatedLinks.map((link, index) => (
            <Link
              key={index}
              href={link.href}
              className="group bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 border border-gray-200 hover:border-emerald-300"
            >
              <h3 className="text-lg font-semibold text-gray-900 group-hover:text-emerald-600 transition-colors mb-2">
                {link.title}
              </h3>
              <p className="text-gray-600 text-sm leading-relaxed">
                {link.description}
              </p>
              <div className="mt-4 flex items-center text-emerald-600 text-sm font-medium">
                Learn more
                <svg className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}

// Generate contextual related links based on current page
function generateRelatedLinks(
  currentPage: string,
  location?: string,
  propertyType?: string,
  transactionType?: string
): RelatedLink[] {
  const links: RelatedLink[] = [];

  // Homepage related links
  if (currentPage === 'homepage') {
    links.push(
      {
        title: 'Explore All Locations',
        href: '/locations',
        description: 'Discover the best areas to live in Bali, from beachfront Canggu to cultural Ubud.',
        priority: 'high'
      },
      {
        title: 'Property Types Guide',
        href: '/property-types',
        description: 'Learn about villas, guesthouses, apartments, and land investment opportunities.',
        priority: 'high'
      },
      {
        title: 'Our Services',
        href: '/services',
        description: 'Investment consultation, property purchase assistance, and viewing arrangements.',
        priority: 'medium'
      }
    );
  }

  // Location page related links
  if (currentPage === 'location' && location) {
    const otherLocations = ['canggu', 'seminyak', 'ubud', 'sanur', 'jimbaran'].filter(loc => loc !== location);
    
    // Add property type links for current location
    links.push(
      {
        title: `${formatLocation(location)} Villa Rentals`,
        href: `/${location}/villa/long-term-rental`,
        description: `Discover luxury villas for long-term rental in ${formatLocation(location)}.`,
        priority: 'high'
      },
      {
        title: `${formatLocation(location)} Villas for Sale`,
        href: `/${location}/villa/for-sale-freehold`,
        description: `Investment opportunities in ${formatLocation(location)} freehold villas.`,
        priority: 'high'
      }
    );

    // Add related locations
    otherLocations.slice(0, 2).forEach(otherLocation => {
      links.push({
        title: `${formatLocation(otherLocation)} Properties`,
        href: `/locations/${otherLocation}`,
        description: `Explore real estate opportunities in ${formatLocation(otherLocation)}.`,
        priority: 'medium'
      });
    });
  }

  // Property type page related links
  if (currentPage === 'property-type' && propertyType) {
    const otherTypes = ['villa', 'guesthouse', 'apartment', 'land'].filter(type => type !== propertyType);
    
    // Add location-specific links for current property type
    ['canggu', 'ubud', 'seminyak'].forEach(loc => {
      links.push({
        title: `${formatPropertyType(propertyType)} in ${formatLocation(loc)}`,
        href: `/locations/${loc}`,
        description: `Find ${formatPropertyType(propertyType).toLowerCase()} properties in ${formatLocation(loc)}.`,
        priority: 'high'
      });
    });

    // Add other property types
    otherTypes.slice(0, 2).forEach(otherType => {
      links.push({
        title: `${formatPropertyType(otherType)} Properties`,
        href: `/property-types/${otherType}`,
        description: `Explore ${formatPropertyType(otherType).toLowerCase()} options across Bali.`,
        priority: 'medium'
      });
    });
  }

  // Specific property + location page links
  if (currentPage === 'property-location' && location && propertyType) {
    const otherTransactions = ['long-term-rental', 'for-sale-freehold', 'for-sale-leasehold']
      .filter(type => type !== transactionType);

    // Add other transaction types for same location/property
    otherTransactions.forEach(transaction => {
      links.push({
        title: `${formatLocation(location)} ${formatPropertyType(propertyType)} ${formatTransactionType(transaction)}`,
        href: `/${location}/${propertyType}/${transaction}`,
        description: `${formatTransactionType(transaction)} options for ${formatPropertyType(propertyType).toLowerCase()} in ${formatLocation(location)}.`,
        priority: 'high'
      });
    });

    // Add general location and property type pages
    links.push(
      {
        title: `All ${formatLocation(location)} Properties`,
        href: `/locations/${location}`,
        description: `Browse all property types available in ${formatLocation(location)}.`,
        priority: 'medium'
      },
      {
        title: `${formatPropertyType(propertyType)} Properties`,
        href: `/property-types/${propertyType}`,
        description: `Explore ${formatPropertyType(propertyType).toLowerCase()} options across all Bali locations.`,
        priority: 'medium'
      }
    );
  }

  // Services page related links
  if (currentPage === 'services') {
    links.push(
      {
        title: 'Investment Consultation',
        href: 'https://app.balipropertyscout.com?query=investment-consultation',
        description: 'Get expert advice on Bali property investment opportunities.',
        priority: 'high'
      },
      {
        title: 'Schedule Property Viewing',
        href: 'https://app.balipropertyscout.com?query=schedule-viewing',
        description: 'Book a viewing for properties that interest you.',
        priority: 'high'
      },
      {
        title: 'Contact Our Team',
        href: '/contact',
        description: 'Speak directly with our local property experts.',
        priority: 'medium'
      }
    );
  }

  // Always add high-value pages if not already included
  const hasContactLink = links.some(link => link.href === '/contact');
  const hasServicesLink = links.some(link => link.href === '/services');

  if (!hasContactLink && links.length < 6) {
    links.push({
      title: 'Contact Our Experts',
      href: '/contact',
      description: 'Get personalized assistance from our local property experts.',
      priority: 'low'
    });
  }

  if (!hasServicesLink && links.length < 6) {
    links.push({
      title: 'Our Services',
      href: '/services',
      description: 'Comprehensive real estate services for all your Bali property needs.',
      priority: 'low'
    });
  }

  // Sort by priority and limit to 6 links
  return links
    .sort((a, b) => {
      const priorityOrder = { high: 0, medium: 1, low: 2 };
      return priorityOrder[a.priority] - priorityOrder[b.priority];
    })
    .slice(0, 6);
}

// Utility functions
function formatLocation(location: string): string {
  const locationMap: Record<string, string> = {
    'canggu': 'Canggu',
    'seminyak': 'Seminyak',
    'ubud': 'Ubud',
    'sanur': 'Sanur',
    'jimbaran': 'Jimbaran',
    'pererenan': 'Pererenan'
  };
  return locationMap[location] || location.charAt(0).toUpperCase() + location.slice(1);
}

function formatPropertyType(propertyType: string): string {
  const typeMap: Record<string, string> = {
    'villa': 'Villa',
    'guesthouse': 'Guesthouse',
    'apartment': 'Apartment',
    'land': 'Land'
  };
  return typeMap[propertyType] || propertyType.charAt(0).toUpperCase() + propertyType.slice(1);
}

function formatTransactionType(transactionType: string): string {
  const typeMap: Record<string, string> = {
    'long-term-rental': 'Long-term Rental',
    'for-sale-freehold': 'For Sale (Freehold)',
    'for-sale-leasehold': 'For Sale (Leasehold)',
    'short-term-rental': 'Short-term Rental'
  };
  return typeMap[transactionType] || transactionType.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

export default InternalLinking;
