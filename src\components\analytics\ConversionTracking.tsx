/**
 * Advanced Conversion Tracking Component
 * STORY-007: Analytics & Conversion Tracking
 */

'use client';

import { useEffect } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import { event, trackPropertyInquiry, trackContactForm, trackLeadGeneration } from './GoogleAnalytics';

// Conversion tracking events
export const trackChatbotClick = (query: string, source: string) => {
  event({
    action: 'chatbot_click',
    category: 'engagement',
    label: `${query} from ${source}`,
    value: 1,
  });

  // Track as potential lead
  trackLeadGeneration({
    currency: 'USD',
    value: 75, // Estimated chatbot lead value
    lead_type: 'property_inquiry',
    property_type: extractPropertyType(query),
    location: extractLocation(query),
  });
};

export const trackLocationPageView = (location: string, propertyType?: string) => {
  event({
    action: 'location_page_view',
    category: 'engagement',
    label: `${location}${propertyType ? ` - ${propertyType}` : ''}`,
    value: 1,
  });
};

export const trackPropertyTypeView = (propertyType: string) => {
  event({
    action: 'property_type_view',
    category: 'engagement',
    label: propertyType,
    value: 1,
  });
};

export const trackSearchIntent = (searchTerm: string, results?: number) => {
  event({
    action: 'search',
    category: 'engagement',
    label: searchTerm,
    value: results || 0,
  });
};

export const trackDownload = (fileName: string, fileType: string) => {
  event({
    action: 'download',
    category: 'engagement',
    label: `${fileName} (${fileType})`,
    value: 1,
  });
};

export const trackEmailClick = (emailType: string) => {
  event({
    action: 'email_click',
    category: 'engagement',
    label: emailType,
    value: 1,
  });
};

export const trackPhoneClick = () => {
  event({
    action: 'phone_click',
    category: 'engagement',
    label: 'phone_number',
    value: 1,
  });

  trackLeadGeneration({
    currency: 'USD',
    value: 200, // High value for phone calls
    lead_type: 'phone_call',
  });
};

export const trackSocialClick = (platform: string) => {
  event({
    action: 'social_click',
    category: 'engagement',
    label: platform,
    value: 1,
  });
};

export const trackTimeOnPage = (timeInSeconds: number, pagePath: string) => {
  if (timeInSeconds > 30) { // Only track meaningful engagement
    event({
      action: 'time_on_page',
      category: 'engagement',
      label: pagePath,
      value: Math.round(timeInSeconds),
    });
  }
};

export const trackScrollDepth = (percentage: number, pagePath: string) => {
  if (percentage >= 25 && percentage % 25 === 0) { // Track at 25%, 50%, 75%, 100%
    event({
      action: 'scroll_depth',
      category: 'engagement',
      label: `${percentage}% - ${pagePath}`,
      value: percentage,
    });
  }
};

// Utility functions
function extractPropertyType(query: string): string | undefined {
  const types = ['villa', 'guesthouse', 'apartment', 'land'];
  return types.find(type => query.toLowerCase().includes(type));
}

function extractLocation(query: string): string | undefined {
  const locations = ['canggu', 'ubud', 'seminyak', 'sanur', 'jimbaran'];
  return locations.find(location => query.toLowerCase().includes(location));
}

// Hook for automatic page tracking
export function usePageTracking() {
  const pathname = usePathname();

  // Safely get search params to avoid SSR issues
  let searchParams: URLSearchParams | null = null;
  try {
    searchParams = useSearchParams();
  } catch (error) {
    // Handle cases where useSearchParams is not available (like 404 pages)
    searchParams = null;
  }

  useEffect(() => {
    const startTime = Date.now();
    let scrollDepthTracked = 0;

    // Track scroll depth
    const handleScroll = () => {
      const scrollPercent = Math.round(
        (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100
      );
      
      if (scrollPercent > scrollDepthTracked && scrollPercent >= 25) {
        const nextMilestone = Math.ceil(scrollPercent / 25) * 25;
        if (nextMilestone <= 100) {
          trackScrollDepth(nextMilestone, pathname);
          scrollDepthTracked = nextMilestone;
        }
      }
    };

    // Track time on page when leaving
    const handleBeforeUnload = () => {
      const timeOnPage = (Date.now() - startTime) / 1000;
      trackTimeOnPage(timeOnPage, pathname);
    };

    // Track specific page types
    if (pathname.startsWith('/locations/')) {
      const location = pathname.split('/')[2];
      trackLocationPageView(location);
    } else if (pathname.startsWith('/property-types/')) {
      const propertyType = pathname.split('/')[2];
      trackPropertyTypeView(propertyType);
    }

    // Add event listeners
    window.addEventListener('scroll', handleScroll, { passive: true });
    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('beforeunload', handleBeforeUnload);
      
      // Track time on page when component unmounts
      const timeOnPage = (Date.now() - startTime) / 1000;
      trackTimeOnPage(timeOnPage, pathname);
    };
  }, [pathname, searchParams]);
}

// Component for automatic tracking
export function ConversionTracking() {
  usePageTracking();
  return null;
}

export default ConversionTracking;
