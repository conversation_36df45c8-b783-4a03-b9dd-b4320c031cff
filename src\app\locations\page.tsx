import { Metadata } from 'next'
import { getLocations } from '@/lib/payload'
import { CleanModernHero as ModernHero } from '@/components/ui/CleanModernHero'
import LocationsPageClient from '@/components/pages/LocationsPageClient'

export const metadata: Metadata = {
  title: 'Best Areas to Live in Bali | Long Term Rental Locations Guide',
  description: 'Discover the best areas to live in Bali for expats and digital nomads. Complete guide to Canggu, Ubud, Seminyak, Sanur and more with rental prices and lifestyle information.',
  keywords: [
    'best areas to live in bali',
    'bali locations for expats',
    'long term rental bali areas',
    'where to live in bali',
    'bali neighborhoods guide',
    'canggu vs ubud vs seminyak',
    'bali expat areas',
    'digital nomad bali locations'
  ],
  openGraph: {
    title: 'Best Areas to Live in Bali | Complete Location Guide',
    description: 'Discover the perfect area for your Bali lifestyle. Compare Canggu, Ubud, Seminyak, Sanur and more with detailed insights on rentals, amenities and community.',
    type: 'website',
  }
}

export default async function LocationsPage() {
  const locations = await getLocations()

  return (
    <main className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100">
      {/* Modern Hero Section */}
      <ModernHero
        title="Explore Bali Locations"
        subtitle="Discover the perfect area for your long-term rental or property investment in Bali. Each location offers unique lifestyle benefits and opportunities, from vibrant beach towns to serene cultural centers."
        backgroundImage="https://images.unsplash.com/photo-1537953773345-d172ccf13cf1?q=80&w=2070&auto=format&fit=crop"
        primaryCTA={{
          text: "Browse All Areas",
          href: "#locations"
        }}
        secondaryCTA={{
          text: "Get Expert Advice",
          href: "/contact"
        }}
      />

      {/* Premium Locations Grid */}
      <LocationsPageClient locations={locations} />

      {/* Premium CTA Section */}
      <section className="py-24 relative overflow-hidden">
        {/* Premium Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-emerald-600 via-emerald-500 to-teal-600" />
        <div className="absolute inset-0 opacity-30" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`
        }} />

        <div className="relative max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl lg:text-5xl font-bold text-white mb-6">
            Ready to Find Your Perfect
            <span className="block text-emerald-200">Bali Location?</span>
          </h2>
          <p className="text-xl text-emerald-100 mb-12 max-w-3xl mx-auto leading-relaxed">
            Get personalized recommendations based on your lifestyle, budget, and preferences. Our local experts will help you find the ideal area for your Bali adventure.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
            <a
              href="/contact"
              className="group relative inline-flex items-center px-8 py-4 text-lg font-semibold text-emerald-600 bg-white rounded-full shadow-2xl hover:shadow-white/25 transition-all duration-500 transform hover:scale-105 overflow-hidden"
            >
              <div className="absolute inset-0 bg-emerald-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              <span className="relative z-10 flex items-center">
                Get Expert Consultation
                <svg className="w-5 h-5 ml-3 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </span>
            </a>
            
            <a
              href="/chatbot"
              className="inline-flex items-center px-8 py-4 text-lg font-semibold text-white bg-white/10 backdrop-blur-sm border border-white/20 rounded-full hover:bg-white/20 hover:border-white/30 transition-all duration-300 transform hover:scale-105"
            >
              <span className="flex items-center">
                Try AI Assistant
                <svg className="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </span>
            </a>
          </div>
        </div>
      </section>
    </main>
  )
}
