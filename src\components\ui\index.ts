/**
 * UI Components - Main Export
 * 
 * This file exports all UI components for easy importing throughout the application.
 */

// Button components
export {
  Button,
  ButtonGroup,
  IconButton,
  type ButtonProps,
  type ButtonGroupProps,
  type IconButtonProps,
  type ButtonVariant,
  type ButtonSize,
} from './Button';

// Card components
export {
  Card,
  CardHeader,
  CardBody,
  CardFooter,
  PropertyCard,
  LocationCard,
  type CardProps,
  type CardHeaderProps,
  type CardBodyProps,
  type CardFooterProps,
  type PropertyCardProps,
  type LocationCardProps,
  type CardVariant,
  type CardPadding,
} from './Card';

// Input components
export {
  Input,
  Textarea,
  Select,
  Checkbox,
  type InputProps,
  type TextareaProps,
  type SelectProps,
  type CheckboxProps,
  type SelectOption,
  type InputSize,
  type InputVariant,
} from './Input';

// Modal components
export {
  Modal,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ConfirmationModal,
  type ModalProps,
  type ModalHeaderProps,
  type ModalBodyProps,
  type ModalFooterProps,
  type ConfirmationModalProps,
  type ModalSize,
} from './Modal';

// Loading components
export {
  Loading,
  Skeleton,
  SkeletonText,
  SkeletonCard,
  LoadingOverlay,
  ProgressBar,
  type LoadingProps,
  type SkeletonProps,
  type SkeletonTextProps,
  type SkeletonCardProps,
  type LoadingOverlayProps,
  type ProgressBarProps,
  type LoadingSize,
  type LoadingVariant,
} from './Loading';

// Premium components
export { ModernHero } from './ModernHero';
export { InteractiveCard } from './InteractiveCard';
export { PremiumPropertyCard } from './PremiumPropertyCard';
export { PremiumContactForm } from './PremiumContactForm';

// Legacy components
export { OptimizedImage } from './OptimizedImage';
export { ChatBubble } from './ChatBubble';
export { DarkModeProvider } from './DarkModeProvider';
