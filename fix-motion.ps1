# PowerShell script to replace motion elements with regular elements
$filePath = "src\app\locations\[slug]\page.tsx"

# Read the file content
$content = Get-Content $filePath -Raw

# Replace motion elements with regular elements
$content = $content -replace '<motion\.div([^>]*)>', '<div$1>'
$content = $content -replace '</motion\.div>', '</div>'
$content = $content -replace '<motion\.h1([^>]*)>', '<h1$1>'
$content = $content -replace '</motion\.h1>', '</h1>'
$content = $content -replace '<motion\.h2([^>]*)>', '<h2$1>'
$content = $content -replace '</motion\.h2>', '</h2>'
$content = $content -replace '<motion\.h3([^>]*)>', '<h3$1>'
$content = $content -replace '</motion\.h3>', '</h3>'
$content = $content -replace '<motion\.p([^>]*)>', '<p$1>'
$content = $content -replace '</motion\.p>', '</p>'
$content = $content -replace '<motion\.li([^>]*)>', '<li$1>'
$content = $content -replace '</motion\.li>', '</li>'
$content = $content -replace '<motion\.span([^>]*)>', '<span$1>'
$content = $content -replace '</motion\.span>', '</span>'
$content = $content -replace '<motion\.section([^>]*)>', '<section$1>'
$content = $content -replace '</motion\.section>', '</section>'

# Remove motion-specific props
$content = $content -replace '\s*initial=\{[^}]*\}', ''
$content = $content -replace '\s*animate=\{[^}]*\}', ''
$content = $content -replace '\s*whileInView=\{[^}]*\}', ''
$content = $content -replace '\s*transition=\{[^}]*\}', ''
$content = $content -replace '\s*viewport=\{[^}]*\}', ''

# Write the content back to the file
Set-Content $filePath $content

Write-Host "Motion elements replaced successfully!"
