// Utility functions for handling RichText content from Payload CMS

export function richTextToPlainText(richText: any): string {
  if (!richText) return ''
  
  // Handle string (legacy format)
  if (typeof richText === 'string') {
    return richText.replace(/<[^>]*>/g, '')
  }
  
  // Handle RichText object (Lexical format)
  if (richText.root && richText.root.children) {
    return extractTextFromChildren(richText.root.children)
  }
  
  return ''
}

function extractTextFromChildren(children: any[]): string {
  if (!Array.isArray(children)) return ''
  
  return children
    .map((child) => {
      if (child.type === 'text') {
        return child.text || ''
      }
      
      if (child.children && Array.isArray(child.children)) {
        return extractTextFromChildren(child.children)
      }
      
      return ''
    })
    .join(' ')
    .trim()
}

export function truncateText(text: string, maxLength: number = 150): string {
  if (!text) return ''
  
  if (text.length <= maxLength) return text
  
  // Find the last space before the max length to avoid cutting words
  const truncated = text.substring(0, maxLength)
  const lastSpace = truncated.lastIndexOf(' ')
  
  if (lastSpace > 0) {
    return truncated.substring(0, lastSpace) + '...'
  }
  
  return truncated + '...'
}

export function richTextToTruncatedPlainText(richText: any, maxLength: number = 150): string {
  const plainText = richTextToPlainText(richText)
  return truncateText(plainText, maxLength)
}
