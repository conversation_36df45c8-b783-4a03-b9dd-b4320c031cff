/**
 * Lazy-loaded Quick Contact Form Component
 * 
 * This component dynamically imports the QuickContactForm to reduce initial bundle size
 */

'use client';

import { Suspense, lazy } from 'react';

// Lazy load the QuickContactForm component
const QuickContactForm = lazy(() => import('./QuickContactForm').then(module => ({ default: module.default })));

// Loading component for QuickContactForm
function QuickContactFormLoading() {
  return (
    <div className="w-full max-w-md mx-auto p-4 bg-white rounded-lg shadow-sm border">
      <div className="animate-pulse">
        {/* Form title skeleton */}
        <div className="h-6 bg-gray-200 rounded mb-4"></div>
        
        {/* Form fields skeleton */}
        <div className="space-y-3">
          <div className="h-10 bg-gray-200 rounded"></div>
          <div className="h-10 bg-gray-200 rounded"></div>
          <div className="h-20 bg-gray-200 rounded"></div>
          <div className="h-10 bg-gray-200 rounded w-24"></div>
        </div>
      </div>
    </div>
  );
}

// Lazy QuickContactForm wrapper component
export function LazyQuickContactForm(props: any) {
  return (
    <Suspense fallback={<QuickContactFormLoading />}>
      <QuickContactForm {...props} />
    </Suspense>
  );
}

export default LazyQuickContactForm;
