import { Metadata } from 'next'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/Button'

export const metadata: Metadata = {
  title: 'About Our AI-Powered Real Estate Team | Bali Property Experts',
  description: 'Meet our AI-enhanced real estate team combining cutting-edge technology with deep local expertise. Discover how we revolutionize property search in Bali with personalized AI matching and human insight.',
  keywords: 'AI real estate, Bali property experts, AI property matching, real estate technology, Bali local expertise',
  robots: {
    index: true,
    follow: true,
  },
}

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-emerald-600 to-emerald-700 text-white py-20">
        <div className="max-w-6xl mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                AI-Powered Real Estate,<br />
                <span className="text-emerald-200">Human-Centered Service</span>
              </h1>
              <p className="text-xl mb-8 text-emerald-100 leading-relaxed">
                Our mission: to help expats, digital nomads and investors find their home or investment in Bali since 2019.
                We combine cutting-edge AI technology with deep local expertise, creating personalized property matches
                that traditional methods simply can't achieve.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Button asChild size="lg">
                  <Link href="https://app.balipropertyscout.com?query=how-ai-works">
                    Experience Our AI
                  </Link>
                </Button>
                <Button variant="outline" size="lg" className="bg-white/10 border-white/20 text-white hover:bg-white/20">
                  <Link href="/contact">
                    Meet Our Team
                  </Link>
                </Button>
              </div>
            </div>
            <div className="relative">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
                <div className="flex items-center mb-4">
                  <div className="w-3 h-3 bg-emerald-400 rounded-full mr-2"></div>
                  <div className="w-3 h-3 bg-yellow-400 rounded-full mr-2"></div>
                  <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                </div>
                <div className="text-emerald-100 font-mono text-sm">
                  <div className="mb-2">{'>'} Analyzing your preferences...</div>
                  <div className="mb-2">{'>'} Matching 2,847 properties...</div>
                  <div className="mb-2">{'>'} Found 3 perfect matches!</div>
                  <div className="text-emerald-300">{'>'} Ready to show you ✨</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Story */}
      <section className="py-20">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              The Future of Real Estate is Here
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Born from frustration with traditional property search, we've created an AI-powered platform
              that understands your unique needs and matches you with perfect properties in paradise.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-16 items-center mb-20">
            <div>
              <h3 className="text-3xl font-bold text-gray-900 mb-6">
                Why We Built This
              </h3>
              <div className="space-y-6">
                <div className="flex items-start">
                  <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center mr-4 mt-1">
                    <svg className="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">Traditional Search is Broken</h4>
                    <p className="text-gray-600">Endless scrolling through irrelevant listings, generic recommendations, and wasted time on properties that don't match your actual needs.</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center mr-4 mt-1">
                    <svg className="w-4 h-4 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">Information Overload</h4>
                    <p className="text-gray-600">Complex legal requirements, hidden costs, and overwhelming choices that make property decisions stressful instead of exciting.</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center mr-4 mt-1">
                    <svg className="w-4 h-4 text-emerald-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">Our AI Solution</h4>
                    <p className="text-gray-600">Intelligent matching that learns your preferences, transparent guidance through every step, and personalized recommendations that actually fit your lifestyle.</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-emerald-50 to-blue-50 rounded-2xl p-8">
              <div className="text-center mb-8">
                <div className="w-16 h-16 bg-emerald-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                </div>
                <h4 className="text-2xl font-bold text-gray-900 mb-4">The "Aha!" Moment</h4>
              </div>

              <blockquote className="text-gray-700 italic text-center mb-6">
                "What if we could understand exactly what someone needs in a property - not just bedrooms and price,
                but lifestyle, investment goals, and dreams - then instantly match them with perfect options?"
              </blockquote>

              <div className="text-center">
                <p className="text-sm text-gray-600">— Our founding insight, 2024</p>
              </div>
            </div>
          </div>

          {/* Team Section */}
          <div className="bg-white rounded-2xl shadow-xl p-12">
            <div className="text-center mb-12">
              <h3 className="text-3xl font-bold text-gray-900 mb-4">
                Meet Your AI-Enhanced Team
              </h3>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Our unique approach combines artificial intelligence with genuine human expertise,
                local knowledge, and personalized service.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8 mb-12">
              <div className="text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-6">
                  <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                </div>
                <h4 className="text-xl font-bold text-gray-900 mb-3">AI Property Matcher</h4>
                <p className="text-gray-600 mb-4">
                  Our intelligent system analyzes thousands of properties and your unique preferences
                  to find perfect matches in seconds, not weeks.
                </p>
                <div className="text-sm text-emerald-600 font-medium">
                  ✓ 24/7 availability ✓ Instant responses ✓ Learning algorithm
                </div>
              </div>

              <div className="text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                  <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <h4 className="text-xl font-bold text-gray-900 mb-3">Local Experts</h4>
                <p className="text-gray-600 mb-4">
                  Our human team brings years of Bali experience, legal knowledge, and cultural
                  insights that no AI can replace.
                </p>
                <div className="text-sm text-blue-600 font-medium">
                  ✓ 10+ years experience ✓ Legal expertise ✓ Cultural knowledge
                </div>
              </div>

              <div className="text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                  <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                </div>
                <h4 className="text-xl font-bold text-gray-900 mb-3">Personal Concierge</h4>
                <p className="text-gray-600 mb-4">
                  From first contact to keys in hand, we provide personalized support for every
                  step of your property journey.
                </p>
                <div className="text-sm text-purple-600 font-medium">
                  ✓ Personal attention ✓ End-to-end support ✓ After-sale care
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-emerald-50 to-blue-50 rounded-xl p-8 text-center">
              <h4 className="text-2xl font-bold text-gray-900 mb-4">
                The Perfect Balance
              </h4>
              <p className="text-lg text-gray-600 mb-6 max-w-3xl mx-auto">
                AI handles the heavy lifting of property matching and data analysis, while our human experts
                provide the personal touch, local insights, and emotional intelligence that make your
                property journey smooth and successful.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg">
                  <Link href="https://app.balipropertyscout.com?query=how-it-works">
                    Try Our AI Assistant
                  </Link>
                </Button>
                <Button variant="outline" size="lg" asChild>
                  <Link href="/contact">
                    Talk to Our Team
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Company Story - PRD Requirement */}
      <section className="py-20 bg-white">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-16">
            <h3 className="text-3xl font-bold text-gray-900 mb-6">
              Our Story
            </h3>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Founded by Indonesian and international partners, we combine local roots with global perspective.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
            <div>
              <h4 className="text-2xl font-bold text-gray-900 mb-6">Our Mission & Values</h4>
              <div className="space-y-4 text-gray-600">
                <p>
                  <strong className="text-gray-900">Mission:</strong> To help expats, digital nomads and investors
                  find their home or investment in Bali since 2019.
                </p>
                <p>
                  <strong className="text-gray-900">Vision:</strong> Creating seamless property experiences through
                  technology and human expertise.
                </p>
                <div>
                  <strong className="text-gray-900">Values:</strong>
                  <ul className="mt-2 space-y-1 ml-4">
                    <li>• <strong>Integrity:</strong> Honest, transparent dealings in every transaction</li>
                    <li>• <strong>Transparency:</strong> Clear communication and no hidden fees</li>
                    <li>• <strong>Local Community:</strong> Supporting Bali's sustainable development</li>
                    <li>• <strong>Sustainability:</strong> Responsible property development practices</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-emerald-50 rounded-xl p-8">
              <h4 className="text-2xl font-bold text-gray-900 mb-6">Our Track Record</h4>
              <div className="grid grid-cols-2 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-emerald-600 mb-2">500+</div>
                  <div className="text-gray-600">Successful Deals</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-emerald-600 mb-2">5+</div>
                  <div className="text-gray-600">Years Experience</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-emerald-600 mb-2">1000+</div>
                  <div className="text-gray-600">Happy Clients</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-emerald-600 mb-2">24/7</div>
                  <div className="text-gray-600">Support</div>
                </div>
              </div>

              <div className="mt-8 text-center">
                <Link
                  href="https://app.balipropertyscout.com?query=meet-our-team"
                  className="inline-flex items-center bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors"
                >
                  Meet Our Team
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* AI Transparency & Ethics */}
      <section className="py-20 bg-gradient-to-br from-emerald-50 via-blue-50 to-purple-50">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-16">
            <h3 className="text-3xl font-bold text-gray-900 mb-6">
              Transparent AI, Trustworthy Results
            </h3>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              We believe in ethical AI that empowers, not replaces, human decision-making.
              Here's how we ensure our technology serves your best interests.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
            <div>
              <h4 className="text-2xl font-bold text-gray-900 mb-6">Our AI Principles</h4>
              <div className="space-y-6">
                <div className="flex items-start">
                  <div className="w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center mr-4 mt-1">
                    <svg className="w-4 h-4 text-emerald-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h5 className="font-semibold text-gray-900 mb-2">Always Explainable</h5>
                    <p className="text-gray-600">Our AI shows you exactly why it recommends specific properties, so you understand the reasoning behind every suggestion.</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-4 mt-1">
                    <svg className="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h5 className="font-semibold text-gray-900 mb-2">Privacy First</h5>
                    <p className="text-gray-600">Your personal data and preferences are encrypted and never shared. Our AI learns to help you, not to profile you for others.</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mr-4 mt-1">
                    <svg className="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h5 className="font-semibold text-gray-900 mb-2">Human Oversight</h5>
                    <p className="text-gray-600">Every AI recommendation is reviewed by our local experts who add context, verify accuracy, and ensure cultural appropriateness.</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-2xl p-8 shadow-lg">
              <div className="text-center mb-6">
                <div className="w-16 h-16 bg-gradient-to-br from-emerald-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5-6v6a2 2 0 01-2 2H6a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2z" />
                  </svg>
                </div>
                <h5 className="text-xl font-bold text-gray-900">AI + Human = Better Results</h5>
              </div>

              <div className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-emerald-50 rounded-lg">
                  <span className="text-sm font-medium text-gray-700">Property Analysis Speed</span>
                  <span className="text-emerald-600 font-bold">1000x faster</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                  <span className="text-sm font-medium text-gray-700">Match Accuracy</span>
                  <span className="text-blue-600 font-bold">94% success rate</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-purple-50 rounded-lg">
                  <span className="text-sm font-medium text-gray-700">Client Satisfaction</span>
                  <span className="text-purple-600 font-bold">4.9/5 stars</span>
                </div>
              </div>
            </div>
          </div>

          <div className="text-center">
            <div className="grid md:grid-cols-3 gap-8">
              <Link href="https://app.balipropertyscout.com?query=how-ai-works" className="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow group">
                <div className="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:bg-emerald-200 transition-colors">
                  <svg className="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">Try Our AI Chat</h4>
                <p className="text-gray-600 text-sm">Experience intelligent property matching firsthand</p>
              </Link>

              <Link href="/contact" className="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow group">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:bg-blue-200 transition-colors">
                  <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">Meet Our Team</h4>
                <p className="text-gray-600 text-sm">Connect with our local property experts</p>
              </Link>

              <Link href="/" className="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow group">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:bg-purple-200 transition-colors">
                  <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">Start Your Search</h4>
                <p className="text-gray-600 text-sm">Find your perfect Bali property today</p>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
